"use client";

import { useBlogStore } from "@/store/blogStore";

interface BlogPostTagsProps {
  tags?: string[];
}

const BlogPostTags: React.FC<BlogPostTagsProps> = ({ tags }) => {
  const { navigateToTagFilter } = useBlogStore();

  const handleTagClick = (tag: string) => {
    // Use global store to navigate to blog with tag filter
    navigateToTagFilter(tag);
  };

  if (!tags || tags.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-2 mb-6">
      {tags.map((tag, index) => (
        <button
          key={index}
          onClick={() => handleTagClick(tag)}
          className="bg-[#914AC41A] !text-xs lg:text-sm text-primary px-2 lg:px-3 py-1 rounded-full hover:bg-[#914AC430] transition-colors cursor-pointer"
        >
          {tag}
        </button>
      ))}
    </div>
  );
};

export default BlogPostTags;
