"use client";
import React from "react";

interface PasswordStrengthIndicatorProps {
  password: string;
  confirmPassword?: string;
  requirements: {
    minLength: boolean;
    hasUppercase: boolean;
    hasLowercase: boolean;
    hasNumber: boolean;
    hasSpecialChar: boolean;
  };
  score: number;
}

const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  confirmPassword,
  requirements,
  score,
}) => {
  const getStrengthText = (score: number): string => {
    if (score === 0) return "Very Weak";
    if (score === 1) return "Weak";
    if (score === 2) return "Fair";
    if (score === 3) return "Good";
    if (score === 4) return "Strong";
    return "Very Strong";
  };

  const getStrengthColor = (score: number): string => {
    if (score === 0) return "bg-red-500";
    if (score === 1) return "bg-red-400";
    if (score === 2) return "bg-yellow-500";
    if (score === 3) return "bg-blue-500";
    if (score === 4) return "bg-green-500";
    return "bg-green-600";
  };

  const getStrengthTextColor = (score: number): string => {
    if (score <= 1) return "text-red-500";
    if (score === 2) return "text-yellow-600";
    if (score === 3) return "text-blue-500";
    return "text-green-600";
  };

  // Check if passwords match (only if confirmPassword is provided and not empty)
  const passwordsMatch = !confirmPassword || confirmPassword === "" || password === confirmPassword;
  const showPasswordMatch = confirmPassword && confirmPassword.length > 0;

  if (!password) return null;

  return (
    <div className="mt-2 space-y-3">
      {/* Strength Meter */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-600">Password Strength</span>
          <span className={`text-xs font-medium ${getStrengthTextColor(score)}`}>
            {getStrengthText(score)}
          </span>
        </div>
        
        <div className="flex space-x-1">
          {[1, 2, 3, 4, 5].map((level) => (
            <div
              key={level}
              className={`h-2 flex-1 rounded-full transition-colors duration-200 ${
                level <= score
                  ? getStrengthColor(score)
                  : "bg-gray-200"
              }`}
            />
          ))}
        </div>
      </div>

      {/* Requirements Checklist */}
      <div className="space-y-1">
        <div className="text-xs text-gray-600 mb-2">Password must contain:</div>
        
        <div className="grid grid-cols-1 gap-1 text-xs">
          <div className={`flex items-center space-x-2 ${
            requirements.minLength ? "text-green-600" : "text-gray-500"
          }`}>
            <span className={`w-4 h-4 rounded-full flex items-center justify-center text-xs ${
              requirements.minLength 
                ? "bg-green-100 text-green-600" 
                : "bg-gray-100 text-gray-400"
            }`}>
              {requirements.minLength ? "✓" : "○"}
            </span>
            <span>At least 8 characters</span>
          </div>
          
          <div className={`flex items-center space-x-2 ${
            requirements.hasUppercase ? "text-green-600" : "text-gray-500"
          }`}>
            <span className={`w-4 h-4 rounded-full flex items-center justify-center text-xs ${
              requirements.hasUppercase 
                ? "bg-green-100 text-green-600" 
                : "bg-gray-100 text-gray-400"
            }`}>
              {requirements.hasUppercase ? "✓" : "○"}
            </span>
            <span>One uppercase letter (A-Z)</span>
          </div>
          
          <div className={`flex items-center space-x-2 ${
            requirements.hasLowercase ? "text-green-600" : "text-gray-500"
          }`}>
            <span className={`w-4 h-4 rounded-full flex items-center justify-center text-xs ${
              requirements.hasLowercase 
                ? "bg-green-100 text-green-600" 
                : "bg-gray-100 text-gray-400"
            }`}>
              {requirements.hasLowercase ? "✓" : "○"}
            </span>
            <span>One lowercase letter (a-z)</span>
          </div>
          
          <div className={`flex items-center space-x-2 ${
            requirements.hasNumber ? "text-green-600" : "text-gray-500"
          }`}>
            <span className={`w-4 h-4 rounded-full flex items-center justify-center text-xs ${
              requirements.hasNumber 
                ? "bg-green-100 text-green-600" 
                : "bg-gray-100 text-gray-400"
            }`}>
              {requirements.hasNumber ? "✓" : "○"}
            </span>
            <span>One number (0-9)</span>
          </div>
          
          <div className={`flex items-center space-x-2 ${
            requirements.hasSpecialChar ? "text-green-600" : "text-gray-500"
          }`}>
            <span className={`w-4 h-4 rounded-full flex items-center justify-center text-xs ${
              requirements.hasSpecialChar 
                ? "bg-green-100 text-green-600" 
                : "bg-gray-100 text-gray-400"
            }`}>
              {requirements.hasSpecialChar ? "✓" : "○"}
            </span>
            <span>One special character (!@#$%^&*)</span>
          </div>

          {/* Password Match Check */}
          {showPasswordMatch && (
            <div className={`flex items-center space-x-2 ${
              passwordsMatch ? "text-green-600" : "text-red-500"
            }`}>
              <span className={`w-4 h-4 rounded-full flex items-center justify-center text-xs ${
                passwordsMatch
                  ? "bg-green-100 text-green-600"
                  : "bg-red-100 text-red-500"
              }`}>
                {passwordsMatch ? "✓" : "✗"}
              </span>
              <span>Passwords match</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PasswordStrengthIndicator;
