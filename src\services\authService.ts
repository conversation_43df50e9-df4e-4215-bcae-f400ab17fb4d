import http from "./httpService";
import storageService, { User } from "./storageService";

// Type definitions
interface RegisterData {
  email: string;
  password: string;
  confirm_password: string;
  first_name: string;
  last_name: string;
}

interface VerifyEmailData {
  email: string;
  otp: string;
}

interface ResendOtpData {
  email: string;
}

interface LoginData {
  email: string;
  password: string;
}

interface AuthResponse {
  success: boolean;
  message?: string;
  token?: string;
  access?: string;
  refresh?: string;
  tokens?: {
    access: string;
    refresh: string;
  };
  user?: User;
  error?: string;
  statusCode?: number;
  fieldErrors?: Record<string, string[]>;
}

interface RefreshTokenResponse {
  access: string;
  refresh?: string;
}

// API response format for registration
interface RegisterResponse {
  message: string;
  user: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
    is_verified: boolean;
    date_joined: string;
  };
}

/**
 * Register a new user
 */
export const register = async (data: RegisterData): Promise<AuthResponse> => {
  try {
    // Use http service for consistent API calls
    const response = await http.post("/api/accounts/register/", data, {
      skipAuthErrorHandling: true, // Skip auth error handling for register endpoint
    });

    // Include the status code in the response
    const responseData = response.data as RegisterResponse;
    const authResponse: AuthResponse = {
      success: true,
      statusCode: response.status,
    };

    if (responseData && responseData.user) {
      // Transform the API response to our internal format
      authResponse.message = responseData.message;
      authResponse.user = {
        email: responseData.user.email,
        first_name: responseData.user.first_name,
        last_name: responseData.user.last_name,
      };
    } else {
      // If we get here, something unexpected happened with the response format
      authResponse.message = responseData.message || "Registration successful";
    }

    return authResponse;
  } catch (error: any) {
    // Handle error response
    if (error.response && error.response.data) {
      // Check if the error response contains field-specific errors
      const responseData = error.response.data;
      const fieldErrors: Record<string, string[]> = {};
      const generalError = "Registration failed";

      // Check if the response is in the format {"field": ["error message"]}
      if (typeof responseData === "object" && responseData !== null) {
        let hasFieldErrors = false;

        // Extract field-specific errors
        for (const [key, value] of Object.entries(responseData)) {
          if (Array.isArray(value)) {
            fieldErrors[key] = value;
            hasFieldErrors = true;
          }
        }

        // If we found field-specific errors, use them
        if (hasFieldErrors) {
          return {
            success: false,
            fieldErrors,
            statusCode: error.response.status,
          };
        }
      }

      // If no field-specific errors or in a different format, use the message if available
      return {
        success: false,
        error: responseData.message || responseData.detail || generalError,
        statusCode: error.response.status,
      };
    }

    return {
      success: false,
      error: "Registration failed. Please try again later.",
      statusCode: 500, // Default error status code
    };
  }
};

/**
 * Verify email with OTP
 */
export const verifyEmail = async (
  data: VerifyEmailData
): Promise<AuthResponse> => {
  try {
    // Use http service for consistent API calls
    const response = await http.post("/api/accounts/verify-email/", data, {
      skipAuthErrorHandling: true, // Skip auth error handling for verify email endpoint
    });

    // Create a base response with status code
    const authResponse: AuthResponse = {
      success: true, // Default to true for 2xx responses
      statusCode: response.status,
    };

    // If the response has data, include it in the authResponse
    if (response.data) {
      // If the API returns a message but no success flag, consider it successful
      if (
        response.data.message &&
        response.status >= 200 &&
        response.status < 300
      ) {
        authResponse.message = response.data.message;
      } else {
        // Otherwise, include all response data
        Object.assign(authResponse, response.data);
      }
    }

    // If verification is successful, save the tokens and user data
    if (
      (response.status >= 200 && response.status < 300) ||
      response.data.success
    ) {
      console.log(
        "OTP verification successful with status code:",
        response.status
      );
      console.log("Response data structure:", Object.keys(response.data));

      // Handle the case where the API returns access and refresh tokens directly at the root level
      // Format: { message, user, access, refresh }
      if (response.data.access && response.data.refresh && response.data.user) {
        console.log(
          "Found access and refresh tokens directly at root level in OTP verification response"
        );
        console.log("User data from OTP verification response:", {
          email: response.data.user.email,
          first_name: response.data.user.first_name,
          last_name: response.data.user.last_name,
        });

        // Save access token
        storageService.setToken(response.data.access);
        console.log("Access token saved to storage after OTP verification");

        // Save refresh token
        storageService.setRefreshToken(response.data.refresh);
        console.log("Refresh token saved to storage after OTP verification");

        // Create user data object using the user data from the response
        const userData: User = {
          email: response.data.user.email || data.email,
          first_name: response.data.user.first_name || "",
          last_name: response.data.user.last_name || "",
          token: response.data.access, // Store access token in user object for backward compatibility
        };

        // Save user data to storage
        storageService.setUser(userData);
        console.log("User data saved to storage after OTP verification");
      }
      // Handle the case where the API returns tokens in a nested structure
      else if (
        response.data.tokens &&
        response.data.tokens.access &&
        response.data.tokens.refresh
      ) {
        console.log("Found tokens in nested structure after OTP verification");

        // Save access token
        storageService.setToken(response.data.tokens.access);

        // Save refresh token
        storageService.setRefreshToken(response.data.tokens.refresh);

        // Create user data object
        const userData: User = {
          email: data.email,
          first_name: response.data.user?.first_name || "",
          last_name: response.data.user?.last_name || "",
          token: response.data.tokens.access, // Store access token in user object for backward compatibility
        };

        // Save user data to storage
        storageService.setUser(userData);
      }
      // For backward compatibility, also handle the case where the API returns a single token
      else if (response.data.token) {
        console.log("Found legacy token format in OTP verification response");

        const userData: User = {
          email: data.email,
          first_name: response.data.user?.first_name || "",
          last_name: response.data.user?.last_name || "",
          token: response.data.token,
        };

        // Save token and user data to storage
        storageService.setToken(response.data.token);
        storageService.setUser(userData);
      }
      // If no token format is found, log a warning
      else {
        console.warn(
          "OTP verification successful but no tokens found in the response:",
          response.data
        );
      }
    }

    return authResponse;
  } catch (error: any) {
    // Handle error response
    if (error.response && error.response.data) {
      // Check if the error response contains field-specific errors
      const responseData = error.response.data;
      const fieldErrors: Record<string, string[]> = {};
      const generalError = "Email verification failed";

      // Check if the response is in the format {"field": ["error message"]}
      if (typeof responseData === "object" && responseData !== null) {
        let hasFieldErrors = false;

        // Extract field-specific errors
        for (const [key, value] of Object.entries(responseData)) {
          if (Array.isArray(value)) {
            fieldErrors[key] = value;
            hasFieldErrors = true;
          }
        }

        // If we found field-specific errors, use them
        if (hasFieldErrors) {
          return {
            success: false,
            fieldErrors,
            statusCode: error.response.status,
          };
        }
      }

      // If no field-specific errors or in a different format, use the message if available
      return {
        success: false,
        error: responseData.message || responseData.detail || generalError,
        statusCode: error.response.status,
      };
    }

    return {
      success: false,
      error: "Email verification failed. Please try again later.",
      statusCode: 500, // Default error status code
    };
  }
};

/**
 * Login user
 */
export const login = async (data: LoginData): Promise<AuthResponse> => {
  try {
    // Use http service for consistent API calls
    const response = await http.post("/api/accounts/login/", data, {
      skipAuthErrorHandling: true, // Skip auth error handling for login endpoint
    });

    // Include the status code in the response
    const authResponse: AuthResponse = {
      success: response.status >= 200 && response.status < 300, // Set success based on status code
      ...response.data,
      statusCode: response.status,
    };

    // Check if the response has a status code in the 200 range
    if (response.status >= 200 && response.status < 300) {
      console.log("Login successful with status code:", response.status);
      console.log("Response data:", response.data);

      // Handle the case where the API returns access and refresh tokens directly at the root level
      // Format: { message, user, access, refresh }
      if (response.data.access && response.data.refresh && response.data.user) {
        console.log(
          "Found access and refresh tokens directly at root level in login response"
        );
        console.log("User data from login response:", {
          email: response.data.user.email,
          first_name: response.data.user.first_name,
          last_name: response.data.user.last_name,
        });

        // Save access token
        storageService.setToken(response.data.access);
        console.log("Access token saved to storage");

        // Save refresh token
        storageService.setRefreshToken(response.data.refresh);
        console.log("Refresh token saved to storage");

        // Create user data object using the user data from the response
        const userData: User = {
          email: response.data.user.email || data.email,
          first_name: response.data.user.first_name || "",
          last_name: response.data.user.last_name || "",
          token: response.data.access, // Store access token in user object for backward compatibility
        };

        // Save user data to storage
        storageService.setUser(userData);
        console.log("User data saved to storage");
      }
      // Handle the case where the API returns tokens in a nested structure
      else if (
        response.data.tokens &&
        response.data.tokens.access &&
        response.data.tokens.refresh
      ) {
        console.log("Found tokens in nested structure in login response");

        // Save access token
        storageService.setToken(response.data.tokens.access);

        // Save refresh token
        storageService.setRefreshToken(response.data.tokens.refresh);

        // Create user data object
        const userData: User = {
          email: data.email,
          first_name: response.data.user?.first_name || "",
          last_name: response.data.user?.last_name || "",
          token: response.data.tokens.access, // Store access token in user object for backward compatibility
        };

        // Save user data to storage
        storageService.setUser(userData);
      }
      // Handle the case where the API returns access and refresh tokens directly without user data
      else if (response.data.access && response.data.refresh) {
        console.log(
          "Found access and refresh tokens directly in login response without user data"
        );

        // Save access token
        storageService.setToken(response.data.access);

        // Save refresh token
        storageService.setRefreshToken(response.data.refresh);

        // Create user data object
        const userData: User = {
          email: data.email,
          first_name: response.data.user?.first_name || "",
          last_name: response.data.user?.last_name || "",
          token: response.data.access, // Store access token in user object for backward compatibility
        };

        // Save user data to storage
        storageService.setUser(userData);
      }
      // For backward compatibility, also handle the case where the API returns a single token
      else if (authResponse.token) {
        console.log("Found legacy token format");

        const userData: User = {
          email: data.email,
          first_name: authResponse.user?.first_name || "",
          last_name: authResponse.user?.last_name || "",
          token: authResponse.token,
        };

        // Save token and user data to storage
        storageService.setToken(authResponse.token);
        storageService.setUser(userData);
      }
      // If no token format is found, log a warning
      else {
        console.warn(
          "Login successful but no tokens found in the response:",
          response.data
        );
      }
    }

    return authResponse;
  } catch (error: any) {
    // Handle error response
    if (error.response && error.response.data) {
      // Check if the error response contains field-specific errors
      const responseData = error.response.data;
      const fieldErrors: Record<string, string[]> = {};
      const generalError = "Login failed";

      // Check if the response is in the format {"field": ["error message"]}
      if (typeof responseData === "object" && responseData !== null) {
        let hasFieldErrors = false;

        // Extract field-specific errors
        for (const [key, value] of Object.entries(responseData)) {
          if (Array.isArray(value)) {
            fieldErrors[key] = value;
            hasFieldErrors = true;
          }
        }

        // If we found field-specific errors, use them
        if (hasFieldErrors) {
          return {
            success: false,
            fieldErrors,
            statusCode: error.response.status,
          };
        }
      }

      // If no field-specific errors or in a different format, use the message if available
      return {
        success: false,
        error: responseData.message || responseData.detail || generalError,
        statusCode: error.response.status,
      };
    }

    return {
      success: false,
      error: "Login failed. Please try again later.",
      statusCode: 500, // Default error status code
    };
  }
};

/**
 * Logout user
 */
export const logout = (): void => {
  storageService.clearAuth();
};

/**
 * Check if user is authenticated by verifying the token with the server
 * @returns Promise<boolean> - True if authenticated, false otherwise
 */
export const isAuthenticated = async (): Promise<boolean> => {
  // If no token exists, return false immediately
  const token = storageService.getToken();
  if (!token) {
    console.log("No access token found in storage, user is not authenticated");
    return false;
  }

  console.log("Access token found in storage, checking validity with server");

  try {
    // Make a GET request to the check-auth endpoint with the token in the Authorization header
    // Explicitly set useAuth to true to include the token
    const response = await http.get("/api/accounts/check-auth/", {
      useAuth: true,
    });

    // If the request is successful (status code 2xx), the token is valid
    const isValid = response.status >= 200 && response.status < 300;

    if (isValid) {
      console.log("Authentication check successful, token is valid");

      // Get user data from storage
      const userData = storageService.getUser();
      if (userData) {
        console.log("User data found in storage:", {
          email: userData.email,
          first_name: userData.first_name,
          last_name: userData.last_name,
        });
      } else {
        console.warn("Token is valid but no user data found in storage");
      }
    } else {
      console.log("Authentication check failed, token is invalid");
    }

    return isValid;
  } catch (error) {
    // If there's an error (including 401 Unauthorized), the token is invalid
    console.error("Authentication check failed:", error);
    return false;
  }
};

/**
 * Get current user
 */
export const getCurrentUser = (): User | null => {
  return storageService.getUser();
};

/**
 * Refresh the access token using the refresh token
 * @returns Promise<boolean> - True if token refresh was successful, false otherwise
 */
export const refreshToken = async (): Promise<boolean> => {
  // Get the refresh token
  const refreshToken = storageService.getRefreshToken();

  // If no refresh token exists, return false immediately
  if (!refreshToken) {
    console.error("No refresh token available");
    return false;
  }

  try {
    // Use http service for consistent API calls
    const response = await http.post(
      "/api/accounts/token/refresh/",
      { refresh: refreshToken },
      { skipAuthErrorHandling: true } // Skip auth error handling for token refresh
    );

    // Check if the response was successful
    if (
      response.status >= 200 &&
      response.status < 300 &&
      response.data.access
    ) {
      console.log("Token refresh successful");

      // Save the new access token
      storageService.setToken(response.data.access);

      // If a new refresh token was provided, save it too
      if (response.data.refresh) {
        storageService.setRefreshToken(response.data.refresh);
      }

      return true;
    } else {
      console.error("Token refresh failed:", response.data);
      return false;
    }
  } catch (error) {
    console.error("Error refreshing token:", error);
    return false;
  }
};

/**
 * Resend OTP verification code
 */
export const resendOtp = async (data: ResendOtpData): Promise<AuthResponse> => {
  try {
    // Use http service for consistent API calls
    const response = await http.post(
      "/api/accounts/resend-otp/",
      data,
      { skipAuthErrorHandling: true } // Skip auth error handling for resend OTP
    );

    // Create a base response with status code
    const authResponse: AuthResponse = {
      success: true, // Default to true for 2xx responses
      statusCode: response.status,
    };

    // If the response has data, include it in the authResponse
    if (response.data) {
      // If the API returns a message but no success flag, consider it successful
      if (
        response.data.message &&
        response.status >= 200 &&
        response.status < 300
      ) {
        authResponse.message = response.data.message;
      } else {
        // Otherwise, include all response data
        Object.assign(authResponse, response.data);
      }
    }

    return authResponse;
  } catch (error: any) {
    // Handle error response
    if (error.response && error.response.data) {
      // Check if the error response contains field-specific errors
      const responseData = error.response.data;
      const fieldErrors: Record<string, string[]> = {};
      const generalError = "Failed to resend verification code";

      // Check if the response is in the format {"field": ["error message"]}
      if (typeof responseData === "object" && responseData !== null) {
        let hasFieldErrors = false;

        // Extract field-specific errors
        for (const [key, value] of Object.entries(responseData)) {
          if (Array.isArray(value)) {
            fieldErrors[key] = value;
            hasFieldErrors = true;
          }
        }

        // If we found field-specific errors, use them
        if (hasFieldErrors) {
          return {
            success: false,
            fieldErrors,
            statusCode: error.response.status,
          };
        }
      }

      // If no field-specific errors or in a different format, use the message if available
      return {
        success: false,
        error: responseData.message || responseData.detail || generalError,
        statusCode: error.response.status,
      };
    }

    return {
      success: false,
      error: "Failed to resend verification code. Please try again later.",
      statusCode: 500, // Default error status code
    };
  }
};

const authService = {
  register,
  verifyEmail,
  login,
  logout,
  isAuthenticated,
  getCurrentUser,
  resendOtp,
  refreshToken,
};

export default authService;
