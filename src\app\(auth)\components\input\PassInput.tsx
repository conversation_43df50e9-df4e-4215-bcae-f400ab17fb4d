"use client";
import <PERSON><PERSON><PERSON> from "@/ui/icons/input/EyeClose";
import <PERSON><PERSON><PERSON> from "@/ui/icons/input/EyeOpen";
import { ComponentProps, useState, useCallback, memo } from "react";

type PassInputProps = {
  onChange: (value: string) => void;
  confirmPassword?: boolean;
} & Omit<ComponentProps<"input">, "onChange">;

const PassInputComponent: React.FC<PassInputProps> = ({
  onChange,
  className,
  confirmPassword,
  ...rest
}) => {
  const [showPassword, setShowPassword] = useState(false);

  // Optimized onChange handler to prevent unnecessary re-renders
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target.value);
    },
    [onChange]
  );

  // Optimized toggle handler
  const togglePassword = useCallback(() => {
    setShowPassword((prev) => !prev);
  }, []);

  return (
    <div className="flex flex-col gap-2">
      <label
        className="text-secondary"
        htmlFor={confirmPassword ? `confirmPassword` : `password`}
      >
        <span className="text-red-500">*</span>{" "}
        {confirmPassword ? "Confirm Password" : "Password"}
      </label>
      <div className="flex items-center p-4 border focus-within:border-primary border-light-gray rounded-lg gap-2">
        <input
          type={showPassword ? "text" : "password"}
          required
          name={confirmPassword ? `confirmPassword` : `password`}
          autoComplete={confirmPassword ? "new-password" : "current-password"}
          {...rest}
          onChange={handleChange}
          className={`textField__input border-none w-full p-0 focus:border-primary w-full ${className}`}
          data-lpignore="true" // Disable LastPass autofill
        />
        {!showPassword ? (
          <div
            onClick={togglePassword}
            className="pl-2 border-l border-light-gray cursor-pointer"
          >
            <EyeClose />
          </div>
        ) : (
          <div
            onClick={togglePassword}
            className="pl-2 border-l border-light-gray cursor-pointer"
          >
            <EyeOpen />
          </div>
        )}
      </div>
    </div>
  );
};

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(PassInputComponent);
