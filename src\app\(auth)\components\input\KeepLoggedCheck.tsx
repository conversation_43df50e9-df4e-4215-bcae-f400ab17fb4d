import { ComponentProps } from "react";

type KeepLoggedCheckProps = {
  onChange: (value: boolean) => void;
  keepLoggedIn: boolean;
} & Omit<ComponentProps<"input">, "onChange">;

const KeepLoggedCheck: React.FC<KeepLoggedCheckProps> = ({
  keepLoggedIn,
  onChange,
}) => {
  return (
    <div className="flex items-center gap-2">
      <input
        id="keepLoggedIn"
        type="checkbox"
        checked={keepLoggedIn}
        onChange={(e) => onChange(e.target.checked)}
        className="accent-white"
      />
      <label htmlFor="keepLoggedIn" className="text-sm text-secondary">
        Stay Logged in
      </label>
    </div>
  );
};

export default KeepLoggedCheck;
