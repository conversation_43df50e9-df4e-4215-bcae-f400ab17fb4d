"use client";
import { useMemo } from "react";
import { CheckIcon } from "@/ui/icons/general";
import { PricingPlan } from "@/services/pricingService";

// Define types for billing period
type BillingPeriod = "monthly" | "annually";

// Define types for the Pro Plan
interface ProPlan {
  id: string;
  name: string;
  price: number;
  period: string;
  description: string;
  features: string[];
  apiPlanId?: number;
}

interface WhiteLabelPlanSelectorProps {
  apiPricingData: PricingPlan[];
  premiumPlanData: PricingPlan[];
  selectedPlan: string | null;
  billingPeriod: BillingPeriod;
  onPlanSelect: (planId: string) => void;
  onBillingPeriodChange: (period: BillingPeriod) => void;
  onNextStep: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export default function WhiteLabelPlanSelector({
  apiPricingData,
  premiumPlanData,
  selectedPlan,
  billingPeriod,
  onPlanSelect,
  onBillingPeriodChange,
  onNextStep,
  isLoading,
  error,
}: WhiteLabelPlanSelectorProps) {
  // Get the appropriate pricing data based on billing period
  const plans = useMemo(() => {
    // Default plans if API data is not available
    const defaultPlans = [
      {
        id: "basic",
        name: "Pro Plan",
        price: billingPeriod === "monthly" ? 28 : 288,
        period: billingPeriod === "monthly" ? "monthly" : "yearly",
        description:
          "Offer top-tier SEO reports under your own branding. Whether you're a freelancer or growing agency, Pro Plan helps you scale client services without building a team.",
        features: [
          "All DIY features included",
          "Generate beautiful, branded PDF audit reports",
          "Add  your logo, and contact info to impress clients",
          "Local SEO audits tailored for service-area businesses",
        ],
      },
      {
        id: "premium",
        name: "Pro Plan & Embedding",
        price: billingPeriod === "monthly" ? 79 : 758,
        period: billingPeriod === "monthly" ? "monthly" : "yearly",
        description:
          "Built for agencies, SaaS products, and platforms ready to offer seamless, scalable SEO services under their own brand. This plan gives you full control over integration, customization, and delivery—powered by automation and AI.",
        features: [
          "All Pro Plan features included",
          "Full API access & embeddable SEO audit widgets",
          "AI-powered SEO analysis for smarter recommendations",
          "Priority support and dedicated account manager",
        ],
      },
    ];

    // If we have API data, use it
    if (apiPricingData.length > 0 && premiumPlanData.length > 0) {
      const intervalType = billingPeriod === "monthly" ? "month" : "year";
      const proPlanPlans = apiPricingData.filter(
        (plan) => plan.interval === intervalType
      );

      const premiumPlans = premiumPlanData.filter(
        (plan) => plan.interval === intervalType
      );

      if (proPlanPlans.length > 0 && premiumPlans.length > 0) {
        const proPlan = proPlanPlans[0];
        const premiumPlan = premiumPlans[0];

        return [
          {
            id: "basic",
            name: "Pro Plan",
            price: proPlan.price,
            period: billingPeriod === "monthly" ? "monthly" : "yearly",
            description: proPlan.description,
            features: [
              "All DIY features included",
              "Generate beautiful, branded PDF audit reports",
              "Add  your logo, and contact info to impress clients",
              "Local SEO audits tailored for service-area businesses",
            ],
            apiPlanId: parseInt(proPlan.id),
          },
          {
            id: "premium",
            name: "Pro Plan & Embedding",
            price: premiumPlan.price,
            period: billingPeriod === "monthly" ? "monthly" : "yearly",
            description: premiumPlan.description,
            features: [
              "All Pro Plan features included",
              "Full API access & embeddable SEO audit widgets",
              "AI-powered SEO analysis for smarter recommendations",
              "Priority support and dedicated account manager",
            ],
            apiPlanId: parseInt(premiumPlan.id),
          },
        ];
      }
    }

    return defaultPlans;
  }, [apiPricingData, premiumPlanData, billingPeriod]);

  return (
    <div className="p-4 overflow-y-auto">
      <h2 className="text-xl font-bold text-secondary mb-4">Select a Plan</h2>
      <p className="text-secondary mb-6">
        Choose the pro plan that best fits your needs.
      </p>

      {/* Billing period toggle */}
      <div className="flex justify-center mb-8">
        <div className="inline-flex bg-gray-100 p-1 rounded-full">
          <button
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              billingPeriod === "monthly"
                ? "bg-primary text-white"
                : "text-gray-600 hover:bg-gray-200"
            }`}
            onClick={() => onBillingPeriodChange("monthly")}
          >
            Monthly
          </button>
          <button
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              billingPeriod === "annually"
                ? "bg-primary text-white"
                : "text-gray-600 hover:bg-gray-200"
            }`}
            onClick={() => onBillingPeriodChange("annually")}
          >
            Annually
          </button>
        </div>
      </div>

      {/* Vertical plan cards */}
      <div className="space-y-6">
        {plans.map((plan) => {
          return (
            <div
              key={plan.id}
              className={`border rounded-lg p-6 cursor-pointer transition-all ${
                selectedPlan === plan.id
                  ? "border-primary bg-primary/5"
                  : "border-gray-200 hover:border-primary/50"
              }`}
              onClick={() => onPlanSelect(plan.id)}
            >
              <div className="flex flex-col md:flex-row md:items-start gap-4">
                {/* Plan info */}
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-secondary mb-2">
                    {plan.name}
                  </h3>

                  <p className="text-secondary mb-4">{plan.description}</p>

                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <CheckIcon className="w-5 h-5 text-primary-green mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-secondary">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Pricing */}
                <div className="md:w-64 flex-shrink-0 bg-gray-50 p-4 rounded-lg">
                  <div className="text-center">
                    <div className="flex items-center justify-center">
                      <span className="text-3xl font-bold text-primary">
                        $
                        {billingPeriod === "annually"
                          ? (plan.price / 12).toFixed(2)
                          : plan.price.toFixed(2)}
                      </span>
                      <span className="text-gray-500 ml-1">/mo</span>
                    </div>

                    <div className="mt-2 text-sm text-gray-500">
                      {billingPeriod === "annually"
                        ? `Billed annually as $${plan.price.toFixed(2)}`
                        : "Billed monthly"}
                    </div>

                    <button
                      onClick={async (e) => {
                        e.stopPropagation();
                        onPlanSelect(plan.id);
                        await onNextStep();
                      }}
                      className="mt-4 w-full btn btn--primary py-2"
                    >
                      Select Plan
                    </button>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {error && (
        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-lg">
          {error}
        </div>
      )}

      <div className="mt-6 flex justify-between">
        <button
          onClick={() => onBillingPeriodChange(billingPeriod)}
          className="btn btn--outline px-6 py-2"
          disabled={isLoading}
        >
          Back
        </button>
        <button
          onClick={(e) => {
            e.preventDefault();
            onNextStep();
          }}
          className="btn btn--primary px-6 py-2"
          disabled={isLoading || !selectedPlan}
        >
          {isLoading ? (
            <div className="flex items-center">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Processing...
            </div>
          ) : (
            "Next Step"
          )}
        </button>
      </div>
    </div>
  );
}
