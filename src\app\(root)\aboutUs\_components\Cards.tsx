import { FlagIcon, Star2Icon } from "@/ui/icons/general";
import Image from "next/image";
import { ReactNode } from "react";

export default function Cards() {
  return (
    <div className="mt-8 lg:mt-[84px] flex flex-col lg:grid grid-cols-2 gap-4 lg:gap-6">
      <Card
        imageSrc="/images/aboutCard1.jpg"
        icon={<FlagIcon className="w-6 h-6" />}
        title="Our mission"
        desc="To empower local businesses and marketers in Australia with intuitive, AI-driven SEO tools designed to unlock growth on Google AU and beyond."
      />
      <Card
        imageSrc="/images/aboutCard2.jpg"
        icon={<Star2Icon className="w-6 h-6" />}
        title="Our vision"
        desc="A digital landscape where every Australian business, regardless of size, can compete, connect, and grow through smarter visibility and actionable SEO insights."
      />
    </div>
  );
}

type CardProps = {
  icon: ReactNode;
  title: string;
  desc: string;
  imageSrc: string;
};
function Card({ title, desc, icon, imageSrc }: CardProps) {
  return (
    <div className="bg-white p-6 rounded-lg flex gap-4 items-stretch">
      <div className="w-full flex flex-col gap-4 items-start">
        <div className="bg-primary/10 text-secondary py-2 px-6 flex items-center gap-2.5 rounded-lg">
          {icon}
          <div className="text-2xl lg:text-[32px] font-black font-heading">
            {title}
          </div>
        </div>
        <p className="text-sm lg:text-base text-secondary">{desc}</p>
      </div>
      <div className="flex-1 flex items-stretch">
        <div className="relative w-[70px] lg:w-[100px] min-h-[162px] lg:min-h-[155px] h-full rounded-lg overflow-hidden">
          <Image
            src={imageSrc}
            alt=""
            fill
            className="w-full h-full object-cover object-center"
          />
        </div>
      </div>
    </div>
  );
}
