"use client";
import dynamic from "next/dynamic";
import FreeSEOToolbox from "@/components/landing/FreeSEOToolbox";

// Dynamic imports for heavy components to reduce initial bundle size
const Audit = dynamic(() => import("./_/components/resultsBox/Audit"), {
  loading: () => (
    <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
  ),
});
const Backlinks = dynamic(
  () => import("./_/components/resultsBox/links/Backlinks"),
  {
    loading: () => (
      <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
    ),
  }
);
const OnPageSeo = dynamic(
  () => import("./_/components/resultsBox/onPageSeo/OnPageSeo"),
  {
    loading: () => (
      <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
    ),
  }
);
const Recommendations = dynamic(
  () => import("./_/components/resultsBox/Recommendations"),
  {
    loading: () => (
      <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
    ),
  }
);
const ReviewChildPage = dynamic(
  () => import("./_/components/resultsBox/ReviewChildPage"),
  {
    loading: () => (
      <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
    ),
  }
);
const SideAudit = dynamic(() => import("./_/components/resultsBox/SideAudit"), {
  loading: () => (
    <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
  ),
});
const SocialResult = dynamic(
  () => import("./_/components/resultsBox/socialResult/SocialResult"),
  {
    loading: () => (
      <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
    ),
  }
);
const Technology = dynamic(
  () => import("./_/components/resultsBox/technology/Technology"),
  {
    loading: () => (
      <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
    ),
  }
);
const LocalSeo = dynamic(
  () => import("./_/components/resultsBox/localSeo/LocalSeo"),
  {
    loading: () => (
      <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
    ),
  }
);
const Usability = dynamic(
  () => import("./_/components/resultsBox/usability/Usability"),
  {
    loading: () => (
      <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
    ),
  }
);
const Performance = dynamic(
  () => import("./_/components/resultsBox/performance/Performance"),
  {
    loading: () => (
      <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
    ),
  }
);

import {
  analyzeApi,
  checkStatusAnalyzeApi,
  getSharedAnalysisApi,
} from "@/services/analyzeService";
import { useEffect, useState, useRef, useCallback } from "react";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import ProgressBar from "./_/components/ProgressBar";
import localData from "./data.json";
// Import types from our seoAnalyzerTypes file
import { SEOAnalyzerResult } from "@/types/seoAnalyzerTypes";
import DinoLoader from "@/components/loading/Loading";
import paymentService, {
  PaymentStatusResponse,
} from "@/services/paymentService";
import storageService from "@/services/storageService";
import { useAuthStore } from "@/store/authStore";
import TooManyRequestsModal from "@/components/modals/TooManyRequestsModal";
import SEOAuditRequestModal from "@/components/modals/SEOAuditRequestModal";
import whiteLabelService from "@/services/whiteLabelService";

export default function Result() {
  // ===== TESTING FLAGS =====
  // Set this flag to True to load data from local JSON file for offline testing
  // When True, the app will load data from src/app/[urlName]/data.json instead of making API calls
  // This is useful for development and testing when you don't have internet access
  // or want to avoid making unnecessary API calls
  const useMockData = false; // Change to True for offline testing

  // ========================

  // ===== LOADING STATE HARD RULE =====
  // IMPORTANT: Once progress exceeds 5% OR meaningful data becomes available,
  // the loading state should NEVER be shown again for the current analysis.
  // This prevents the page from getting stuck in loading state when data is being loaded in the background.
  // ===================================

  const [taskId, setTaksId] = useState<string>("");
  const [done, setDone] = useState<boolean>(false);
  const [analyzeRequested, setAnalyzeRequested] = useState<boolean>(false);
  const [hasUsefulData, setHasUsefulData] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [isComplete, setIsComplete] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [showAnalyzingText, setShowAnalyzingText] = useState<boolean>(false);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  // Payment status related states
  const [paymentStatus, setPaymentStatus] =
    useState<PaymentStatusResponse | null>(null);
  const [showWhiteLabelModal, setShowWhiteLabelModal] =
    useState<boolean>(false);
  const [isLoadingPayment, setIsLoadingPayment] = useState<boolean>(false);

  // Too Many Requests modal state
  const [showTooManyRequestsModal, setShowTooManyRequestsModal] =
    useState<boolean>(false);
  const [retryAfterSeconds, setRetryAfterSeconds] = useState<
    number | undefined
  >(undefined);

  // SEO Audit Request modal state
  const [showSEOAuditRequestModal, setShowSEOAuditRequestModal] =
    useState<boolean>(false);

  // Use refs to track if API calls have been made to prevent multiple calls
  const analyzeApiCalledRef = useRef<boolean>(false);

  // Ref to track if we've passed the 5% progress threshold to prevent loading state from being re-enabled
  const hasPassedFivePercentRef = useRef<boolean>(false);

  // Individual state management for each analysis field
  const [onpageAnalysis, setOnpageAnalysis] = useState<any>(null);
  const [usabilityAnalysis, setUsabilityAnalysis] = useState<any>(null);
  const [technologyAnalysis, setTechnologyAnalysis] = useState<any>(null);
  const [socialAnalysis, setSocialAnalysis] = useState<any>(null);
  const [performanceAnalysis, setPerformanceAnalysis] = useState<any>(null);
  const [linksAnalysis, setLinksAnalysis] = useState<any>(null);
  const [localSeoAnalysis, setLocalSeoAnalysis] = useState<any>(null);
  const [pagespeedAnalysis, setPagespeedAnalysis] = useState<any>(null);
  const [pagespeedMobileAnalysis, setPagespeedMobileAnalysis] =
    useState<any>(null);
  const [childPages, setChildPages] = useState<any>(null);
  const [desktopScreenshot, setDesktopScreenshot] = useState<string | null>(
    null
  );

  // Simplified state management - single data object for backward compatibility
  const [apiData, setApiData] = useState<Partial<SEOAnalyzerResult>>({});

  const params = useParams<{ urlName: string }>();
  const router = useRouter();
  const searchParams = useSearchParams();
  const urlTaskId = searchParams?.get("share");
  const urlPaymentId = searchParams?.get("uid");

  // Use the taskId from URL if available, otherwise use the one from state
  const effectiveTaskId = urlTaskId || taskId;

  // Track if we've tried the share API and need to fall back to the regular API
  const [shareApiFailed, setShareApiFailed] = useState<boolean>(false);

  // Create a ref to track if we've tried the share API to avoid dependency issues
  const shareApiTriedRef = useRef<boolean>(false);

  // Reset the shareApiTriedRef and hasPassedFivePercentRef when the taskId changes
  useEffect(() => {
    if (shareApiTriedRef.current) {
      shareApiTriedRef.current = false;
      setShareApiFailed(false);
    }
    // Reset the 5% threshold ref for new analysis
    hasPassedFivePercentRef.current = false;
  }, [effectiveTaskId]);

  // Check payment status when uid is present in URL
  useEffect(() => {
    // Skip if no payment ID in URL
    if (!urlPaymentId) return;

    const checkPaymentStatus = async () => {
      try {
        setIsLoadingPayment(true);

        // Get the authentication token
        const token = storageService.getToken();

        if (!token) {
          // No authentication token found. User may need to log in.
          // You can handle this case by showing a login prompt if needed
          // For now, we'll still try to make the API call and let the server handle auth errors
        }

        // Call the payment status API (token will be added by the HTTP service)
        const status = await paymentService.checkPaymentStatus(urlPaymentId);

        if (status) {
          // Update payment status state
          setPaymentStatus(status);

          // Always open the white label modal at the download step when payment data is available
          setShowWhiteLabelModal(true);
        }
      } catch (error: any) {
        // Handle authentication errors
        if (error.response && error.response.status === 401) {
          // Authentication error: Token may be expired or invalid
          // You can handle this by redirecting to login or showing a message
        }
      } finally {
        setIsLoadingPayment(false);
      }
    };

    // Execute the payment status check
    checkPaymentStatus();

    // This effect should only run once when the component mounts
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Helper function to load mock data
  const loadMockData = async () => {
    // Reduce simulated delay to improve performance
    await new Promise((resolve) => setTimeout(resolve, 100));
    return localData;
  };

  const { data } = useQuery({
    queryKey: ["job-status", effectiveTaskId],
    queryFn: async () => {
      // If mock data flag is set to true, return local data
      if (useMockData) {
        return loadMockData();
      }

      // If we have a taskId from URL and haven't tried the share API yet
      if (urlTaskId && !shareApiTriedRef.current) {
        try {
          // Mark that we've tried the share API
          shareApiTriedRef.current = true;

          // Try to get the data from the share API
          const sharedData = await getSharedAnalysisApi(urlTaskId);

          // If we get here, the share API worked
          return sharedData;
        } catch (error) {
          // If the share API fails, mark it as failed for UI purposes
          setShareApiFailed(true);

          // Fall back to the regular API
          return checkStatusAnalyzeApi(effectiveTaskId);
        }
      }

      // Use the regular status API
      return checkStatusAnalyzeApi(effectiveTaskId);
    },
    enabled: useMockData || effectiveTaskId !== "",
    // Increase polling interval to reduce server load and improve performance
    refetchInterval:
      !useMockData && !done && (!urlTaskId || shareApiFailed) ? 3000 : false, // Only poll if not using mock data, not done, and either not using a shared taskId or the share API failed
    // Disable refetching on window focus to prevent unnecessary API calls
    refetchOnWindowFocus: false,
  });

  // Using the SEOAnalyzerResult type from our types file
  // with a slight modification to make properties optional and add results field
  type ApiResultData = Partial<SEOAnalyzerResult> & {
    results?: Record<string, unknown>;
    status?: string;
  };

  // Simplified function to check if we have any data to display
  const hasAnyApiData = useCallback(
    (dataObj: Partial<ApiResultData>): boolean => {
      return !!(dataObj && Object.keys(dataObj).length > 0);
    },
    []
  );

  // Function to calculate progress based on available data
  const calculateProgress = useCallback(
    (result: Partial<ApiResultData>, status?: string): number => {
      if (!result) return 0;

      // If status is "success", return 100% immediately
      if (status === "success") return 100;

      // Helper function to check if a section is complete or in progress
      const checkSection = (
        section: unknown
      ): { completed: boolean; inProgress: boolean } => {
        if (!section) return { completed: false, inProgress: false };

        if (section === "in_progress") {
          return { completed: false, inProgress: true };
        } else if (typeof section === "object" || section === true) {
          return { completed: true, inProgress: false };
        }

        return { completed: false, inProgress: false };
      };

      // Check each section
      const sections = [
        { data: result.desktop_screenshot_url, weight: 1 },
        { data: result.onpage_analysis, weight: 1.5 },
        { data: result.usability_analysis, weight: 1 },
        { data: result.technology_review_analysis, weight: 1 },
        { data: result.social_analysis, weight: 1 },
        { data: result.performance_analysis, weight: 1 },
        { data: result.links_analysis, weight: 1.5 },
        { data: result.pagespeed_analysis, weight: 1 },
        { data: result.pagespeed_mobile_analysis, weight: 1 },
        { data: result.localseo_analysis, weight: 1 },
        { data: result.child_pages, weight: 1 },
      ];

      let totalWeight = 0;
      let completedWeight = 0;
      let inProgressWeight = 0;

      sections.forEach((section) => {
        totalWeight += section.weight;
        const status = checkSection(section.data);

        if (status.completed) {
          completedWeight += section.weight;
        } else if (status.inProgress) {
          inProgressWeight += section.weight * 0.5; // Count in-progress as half complete
        }
      });

      // Calculate percentage based on weighted sections
      const progressPercentage =
        ((completedWeight + inProgressWeight) / totalWeight) * 100;

      // Cap at 99% unless status is "success"
      return Math.min(Math.round(progressPercentage), 99);
    },
    []
  );

  useEffect(() => {
    if (data) {
      // Check if we have a "success" status but with a nested "failed" status and error message
      if (
        data.status === "success" &&
        data.result?.status === "failed" &&
        data.result?.message
      ) {
        setDone(true);
        setProgress(100);
        setIsComplete(true);
        setShowSEOAuditRequestModal(true); // Show the SEO audit request modal
        return; // Exit early to avoid processing this as a successful response
      }

      // If we're using the share API and it succeeded, mark as complete
      // We use the searchParams directly instead of relying on urlTaskId from the dependency array
      const isSharedAnalysis = searchParams?.get("taskId") !== null;
      if (isSharedAnalysis && data.status === "success") {
        setDone(true);
        setProgress(100);
        setIsComplete(true);
      }

      // Check if the analysis is complete
      if (
        data.status === "success" ||
        (data.result?.results && data.result.results.pass === "completed")
      ) {
        setDone(true);
        setProgress(100);
        setIsComplete(true);
      } else if (data.result) {
        // Calculate progress based on available data and status
        const currentProgress = calculateProgress(data.result, data.status);
        setProgress(currentProgress);

        // If we have any data, update the hasUsefulData state
        if (hasAnyApiData(data.result) && !hasUsefulData) {
          setHasUsefulData(true);
        }
      } else if (data.status === "in_progress") {
        // Handle in_progress status without data
        setProgress(Math.max(progress, 5)); // Set at least 5% progress to show something is happening
      }

      // Check if data.result exists and update the individual states immediately
      if (data.result) {
        // Helper function to safely parse JSON strings
        const parseJsonField = (field: any) => {
          if (typeof field === "string") {
            try {
              const parsed = JSON.parse(field);
              return parsed;
            } catch (e) {
              return field;
            }
          }
          return field;
        };

        // Store parsed data in temporary variables to avoid race conditions
        let parsedOnpageAnalysis = null;
        let parsedUsabilityAnalysis = null;
        let parsedTechnologyAnalysis = null;
        let parsedSocialAnalysis = null;
        let parsedPerformanceAnalysis = null;
        let parsedLinksAnalysis = null;
        let parsedLocalSeoAnalysis = null;
        let parsedPagespeedAnalysis = null;
        let parsedPagespeedMobileAnalysis = null;

        // Update individual states for each field when data is received
        if (data.result.onpage_analysis !== undefined) {
          parsedOnpageAnalysis = parseJsonField(data.result.onpage_analysis);
          setOnpageAnalysis(parsedOnpageAnalysis);
        }

        if (data.result.usability_analysis !== undefined) {
          parsedUsabilityAnalysis = parseJsonField(
            data.result.usability_analysis
          );
          setUsabilityAnalysis(parsedUsabilityAnalysis);
        }

        if (data.result.technology_review_analysis !== undefined) {
          parsedTechnologyAnalysis = parseJsonField(
            data.result.technology_review_analysis
          );
          setTechnologyAnalysis(parsedTechnologyAnalysis);
        }

        if (data.result.social_analysis !== undefined) {
          parsedSocialAnalysis = parseJsonField(data.result.social_analysis);
          setSocialAnalysis(parsedSocialAnalysis);
        }

        if (data.result.performance_analysis !== undefined) {
          parsedPerformanceAnalysis = parseJsonField(
            data.result.performance_analysis
          );
          setPerformanceAnalysis(parsedPerformanceAnalysis);
        }

        if (data.result.links_analysis !== undefined) {
          parsedLinksAnalysis = parseJsonField(data.result.links_analysis);
          setLinksAnalysis(parsedLinksAnalysis);
        }

        if (data.result.localseo_analysis !== undefined) {
          parsedLocalSeoAnalysis = parseJsonField(
            data.result.localseo_analysis
          );
          setLocalSeoAnalysis(parsedLocalSeoAnalysis);
        }

        if (data.result.pagespeed_analysis !== undefined) {
          parsedPagespeedAnalysis = parseJsonField(
            data.result.pagespeed_analysis
          );
          setPagespeedAnalysis(parsedPagespeedAnalysis);
        }

        if (data.result.pagespeed_mobile_analysis !== undefined) {
          parsedPagespeedMobileAnalysis = parseJsonField(
            data.result.pagespeed_mobile_analysis
          );
          setPagespeedMobileAnalysis(parsedPagespeedMobileAnalysis);
        }

        if (data.result.child_pages !== undefined) {
          setChildPages(
            data.result.child_pages?.pages || data.result.child_pages
          );
        }

        if (data.result.desktop_screenshot_url !== undefined) {
          setDesktopScreenshot(data.result.desktop_screenshot_url);
        }

        // Update the unified API data state for backward compatibility
        // Use the freshly parsed data to avoid race conditions
        setApiData((prevData) => ({
          ...prevData,
          ...data.result,
          // Use the parsed temporary variables for immediate consistency
          onpage_analysis: parsedOnpageAnalysis || prevData.onpage_analysis,
          usability_analysis:
            parsedUsabilityAnalysis || prevData.usability_analysis,
          technology_review_analysis:
            parsedTechnologyAnalysis || prevData.technology_review_analysis,
          social_analysis: parsedSocialAnalysis || prevData.social_analysis,
          performance_analysis:
            parsedPerformanceAnalysis || prevData.performance_analysis,
          links_analysis: parsedLinksAnalysis || prevData.links_analysis,
          localseo_analysis:
            parsedLocalSeoAnalysis || prevData.localseo_analysis,
          pagespeed_analysis:
            parsedPagespeedAnalysis || prevData.pagespeed_analysis,
          pagespeed_mobile_analysis:
            parsedPagespeedMobileAnalysis || prevData.pagespeed_mobile_analysis,
          child_pages:
            data.result.child_pages?.pages ||
            data.result.child_pages ||
            prevData.child_pages,
          desktop_screenshot_url:
            data.result.desktop_screenshot_url ||
            prevData.desktop_screenshot_url,
        }));

        // Calculate current progress to enforce the 5% rule
        const currentProgress = calculateProgress(data.result, data.status);

        // Track if we've passed the 5% threshold
        if (currentProgress > 5) {
          hasPassedFivePercentRef.current = true;
        }

        // HARD RULE: Never show loading state if progress > 5% AND we have data
        // Exit loading state when we have any data AND progress > 5%
        if (hasAnyApiData(data.result) && currentProgress > 5) {
          setLoading(false);
          if (!hasUsefulData) {
            setHasUsefulData(true);
          }
        }
        // If we have data but progress is still <= 5%, only exit loading if we have substantial data
        else if (hasAnyApiData(data.result)) {
          // Check if we have at least one meaningful analysis section completed
          const hasMeaningfulData = !!(
            (data.result.onpage_analysis &&
              data.result.onpage_analysis !== "in_progress") ||
            (data.result.usability_analysis &&
              data.result.usability_analysis !== "in_progress") ||
            (data.result.technology_review_analysis &&
              data.result.technology_review_analysis !== "in_progress") ||
            (data.result.performance_analysis &&
              data.result.performance_analysis !== "in_progress") ||
            (data.result.links_analysis &&
              data.result.links_analysis !== "in_progress") ||
            (data.result.localseo_analysis &&
              data.result.localseo_analysis !== "in_progress") ||
            data.result.desktop_screenshot_url
          );

          if (hasMeaningfulData) {
            setLoading(false);
            if (!hasUsefulData) {
              setHasUsefulData(true);
            }
          }
        }

        // Only mark as done if the analysis is truly complete
        if (
          data.status === "success" ||
          (data.result.results && data.result.results.pass === "completed")
        ) {
          setDone(true);
          setIsComplete(true);
          setProgress(100); // Ensure progress is 100% when complete
        }
      } else {
        // If we don't have useful data yet, keep loading state active
        // HARD RULE: Only show loading for very initial state (progress <= 5) AND no meaningful data AND haven't passed 5% threshold
        if (
          progress <= 5 &&
          !hasUsefulData &&
          !hasPassedFivePercentRef.current
        ) {
          setLoading(true);
        } else if (progress > 5 || hasPassedFivePercentRef.current) {
          // ENFORCE: Never show loading if progress > 5% OR we've already passed the 5% threshold
          setLoading(false);
        }
      }
    }
    // Use a stable reference to data to prevent unnecessary re-renders
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.status, data?.result?.status, hasUsefulData, progress]);

  // Get authentication state from the store
  const isAuthenticatedFromStore = useAuthStore(
    (state) => state.isAuthenticated
  );

  // Update local state when store state changes
  useEffect(() => {
    setIsAuthenticated(isAuthenticatedFromStore);
  }, [isAuthenticatedFromStore]);

  // Handle analyzing text visibility with 1-second delay
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (loading) {
      // Start the timeout when loading begins
      timeoutId = setTimeout(() => {
        setShowAnalyzingText(true);
      }, 300);
    } else {
      // Hide the text immediately when loading stops
      setShowAnalyzingText(false);
    }

    // Cleanup timeout on unmount or when loading state changes
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [loading]);

  useEffect(() => {
    // If using mock data, set the necessary states and skip API calls
    if (useMockData) {
      setDone(true);
      setProgress(100);
      setIsComplete(true);
      setHasUsefulData(true);
      setAnalyzeRequested(true);
      analyzeApiCalledRef.current = true;
      // Set a dummy task ID for consistency
      setTaksId("mock-data-task-id");
      return;
    }

    async function fetchData() {
      try {
        // Set the ref to true before making the API call to prevent race conditions
        analyzeApiCalledRef.current = true;
        if (params?.urlName) {
          const { task_id } = await analyzeApi(params.urlName);
          setTaksId(task_id);
          setAnalyzeRequested(true);
        }
      } catch (error: any) {
        // Check if it's a 429 Too Many Requests error
        if (error.response && error.response.status === 429) {
          // Extract the error details
          const errorData = error.response.data;

          // Show the Too Many Requests modal
          if (errorData && errorData.error === "Too Many Requests") {
            // Set retry after seconds if available
            if (errorData.retry_after_seconds) {
              setRetryAfterSeconds(errorData.retry_after_seconds);
            }

            // Show the modal
            setShowTooManyRequestsModal(true);
            return; // Exit early to avoid setting the generic error message
          }
        }

        // For other errors, show the SEO audit request modal
        setShowSEOAuditRequestModal(true);
      }
    }

    // If we have a taskId from URL, set it in state and mark as requested
    if (urlTaskId) {
      setTaksId(urlTaskId);
      setAnalyzeRequested(true);
      analyzeApiCalledRef.current = true;

      // We'll set isComplete based on the API response, not immediately
      // This allows us to fall back to the regular API if needed
      return;
    }

    // Only make the API call if:
    // 1. We have a URL parameter
    // 2. We haven't already requested an analysis (state check)
    // 3. We haven't already called the API (ref check)
    // 4. The URL is valid
    // 5. User hasn't reached the audit limit (if not authenticated)
    if (params?.urlName && !analyzeRequested && !analyzeApiCalledRef.current) {
      if (
        /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]{2,})(\/[^\s]*)?$/.test(
          params.urlName
        )
      ) {
        fetchData();
      } else {
        router.push("/");
      }
    }
  }, [
    params,
    analyzeRequested,
    router,
    urlTaskId,
    useMockData,
    isAuthenticated,
  ]);

  // We'll always render the components, even during loading

  // Remove the error display section since we're using the modal instead

  return (
    <div
      style={{
        fontFamily:
          "ui-sans-serif,-apple-system,system-ui,Segoe UI,Helvetica,Apple Color Emoji,Arial,sans-serif,Segoe UI Emoji,Segoe UI Symbol",
      }}
    >
      {/* Too Many Requests Modal */}
      <div className="">
        <TooManyRequestsModal
          isOpen={showTooManyRequestsModal}
          onClose={() => setShowTooManyRequestsModal(false)}
          retryAfterSeconds={retryAfterSeconds}
        />
      </div>

      {/* SEO Audit Request Modal */}
      <SEOAuditRequestModal
        isOpen={showSEOAuditRequestModal}
        onClose={() => setShowSEOAuditRequestModal(false)}
        websiteUrl={params?.urlName || ""}
      />

      {/* Progress Bar - Always show */}
      <ProgressBar
        progress={
          data && data.result
            ? calculateProgress(data.result, data.status)
            : progress
        }
        isComplete={isComplete}
      />

      <div className="w-full  container my-8  grid grid-cols-1 lg:grid-cols-12 min-[1240px]:flex items-start relative gap-6">
        <div
          className={`lg:col-span-4 min-[1240px]:w-[342px]  lg:sticky top-3 left-0`}
        >
          <SideAudit
            result={data?.result || {}}
            success={isComplete}
            taskId={effectiveTaskId}
            paymentStatus={paymentStatus}
            showWhiteLabelModal={showWhiteLabelModal}
            isLoadingPayment={isLoadingPayment}
            onWhiteLabelModalClose={() => setShowWhiteLabelModal(false)}
            urlPaymentId={urlPaymentId || undefined}
          />
        </div>
        <div className="lg:col-span-8 min-[1240px]:w-[874px]">
          <div className="w-full flex flex-col  gap-8 lg:gap-6">
            {/* Audit/Screenshot section - Always show */}
            <div id="audit">
              {/* Pass data directly to Audit component, just like SideAudit */}
              <Audit
                results={data?.result || {}}
                urlName={params?.urlName || ""}
              />
            </div>

            {/* Recommendations section - Always show */}
            <div id="recommendations">
              <Recommendations
                onPageSeoData={apiData.onpage_analysis || null}
                usabilityData={apiData.usability_analysis || null}
                linksData={apiData.links_analysis || null}
                techseoData={apiData.technology_review_analysis || null}
                socialData={apiData.social_analysis || null}
                performanceData={apiData.performance_analysis || null}
                pagespeedData={apiData.pagespeed_analysis || null}
                pagespeedMobileData={apiData.pagespeed_mobile_analysis || null}
              />
            </div>

            {/* On-Page SEO section - Always show, pass data directly from API result like SideAudit */}
            <div id="onPageSEO">
              {(() => {
                // Get the onpage_analysis from the raw API data (same as SideAudit)
                let rawOnpageData = data?.result?.onpage_analysis;

                // Parse if it's a JSON string (same logic as parseJsonField)
                if (typeof rawOnpageData === "string") {
                  try {
                    rawOnpageData = JSON.parse(rawOnpageData);
                  } catch (e) {
                    // Failed to parse raw onpage data
                  }
                }

                // Use the raw API data if available, fallback to individual state
                const dataToPass = rawOnpageData || onpageAnalysis || {};

                return <OnPageSeo results={dataToPass} />;
              })()}
            </div>

            {/* Rankings section - uses static data for now, but we'll add a loading state for consistency */}
            {/* {hasUsefulData ? <Rankings /> : null} */}

            {/* Links section - Always show, pass data directly from API result like SideAudit */}
            <div id="backlinks">
              {(() => {
                // Get the links_analysis from the raw API data (same as SideAudit)
                let rawLinksData = data?.result?.links_analysis;

                // Parse if it's a JSON string (same logic as parseJsonField)
                if (typeof rawLinksData === "string") {
                  try {
                    rawLinksData = JSON.parse(rawLinksData);
                  } catch (e) {
                    // Failed to parse raw links data
                  }
                }

                // Use the raw API data if available, fallback to individual state
                const linksDataToPass = rawLinksData || linksAnalysis || null;

                return <Backlinks results={linksDataToPass} />;
              })()}
            </div>

            {/* Usability section - Always show, pass data directly from API result like SideAudit */}
            <div id="usability">
              {(() => {
                // Get the usability_analysis from the raw API data (same as SideAudit)
                let rawUsabilityData = data?.result?.usability_analysis;

                // Parse if it's a JSON string (same logic as parseJsonField)
                if (typeof rawUsabilityData === "string") {
                  try {
                    rawUsabilityData = JSON.parse(rawUsabilityData);
                  } catch (e) {
                    // Failed to parse raw usability data
                  }
                }

                // Get pagespeed data from raw API as well
                let rawPagespeedData = data?.result?.pagespeed_analysis;
                let rawPagespeedMobileData =
                  data?.result?.pagespeed_mobile_analysis;

                // Parse pagespeed data if needed
                if (typeof rawPagespeedData === "string") {
                  try {
                    rawPagespeedData = JSON.parse(rawPagespeedData);
                  } catch (e) {
                    // Failed to parse raw pagespeed data
                  }
                }

                if (typeof rawPagespeedMobileData === "string") {
                  try {
                    rawPagespeedMobileData = JSON.parse(rawPagespeedMobileData);
                  } catch (e) {
                    // Failed to parse raw pagespeed mobile data
                  }
                }

                // Use the raw API data if available, fallback to individual state
                const usabilityDataToPass =
                  rawUsabilityData || usabilityAnalysis;
                const pagespeedDataToPass =
                  rawPagespeedData || pagespeedAnalysis;
                const pagespeedMobileDataToPass =
                  rawPagespeedMobileData || pagespeedMobileAnalysis;

                // Combine the data like before for original component
                const combinedUsabilityData = usabilityDataToPass
                  ? {
                      ...usabilityDataToPass,
                      pagespeed_analysis: pagespeedDataToPass,
                      pagespeed_mobile_analysis: pagespeedMobileDataToPass,
                    }
                  : null;

                return <Usability results={combinedUsabilityData} />;
              })()}
            </div>

            {/* Performance section - Always show, pass data directly from API result like SideAudit */}
            <div id="performance">
              {(() => {
                // Get the performance_analysis from the raw API data (same as SideAudit)
                let rawPerformanceData = data?.result?.performance_analysis;

                // Parse if it's a JSON string (same logic as parseJsonField)
                if (typeof rawPerformanceData === "string") {
                  try {
                    rawPerformanceData = JSON.parse(rawPerformanceData);
                  } catch (e) {
                    // Failed to parse raw performance data
                  }
                }

                // Get pagespeed data from raw API as well
                let rawPagespeedData = data?.result?.pagespeed_analysis;
                let rawPagespeedMobileData =
                  data?.result?.pagespeed_mobile_analysis;

                // Parse pagespeed data if needed
                if (typeof rawPagespeedData === "string") {
                  try {
                    rawPagespeedData = JSON.parse(rawPagespeedData);
                  } catch (e) {
                    // Failed to parse raw pagespeed data
                  }
                }

                if (typeof rawPagespeedMobileData === "string") {
                  try {
                    rawPagespeedMobileData = JSON.parse(rawPagespeedMobileData);
                  } catch (e) {
                    // Failed to parse raw pagespeed mobile data
                  }
                }

                // Use the raw API data if available, fallback to individual state
                const performanceDataToPass =
                  rawPerformanceData || performanceAnalysis || null;
                const pagespeedDataToPass =
                  rawPagespeedData || pagespeedAnalysis || null;
                const pagespeedMobileDataToPass =
                  rawPagespeedMobileData || pagespeedMobileAnalysis || null;

                return (
                  <Performance
                    results={performanceDataToPass}
                    pagespeedData={pagespeedDataToPass}
                    pagespeedMobileData={pagespeedMobileDataToPass}
                  />
                );
              })()}
            </div>

            {/* Technology section - Always show, pass data directly from API result like SideAudit */}
            <div id="technology">
              {(() => {
                // Get the technology_review_analysis from the raw API data (same as SideAudit)
                let rawTechnologyData =
                  data?.result?.technology_review_analysis;

                // Parse if it's a JSON string (same logic as parseJsonField)
                if (typeof rawTechnologyData === "string") {
                  try {
                    rawTechnologyData = JSON.parse(rawTechnologyData);
                  } catch (e) {
                    // Failed to parse raw technology data
                  }
                }

                // Use the raw API data if available, fallback to individual state
                const technologyDataToPass =
                  rawTechnologyData || technologyAnalysis || null;

                return <Technology results={technologyDataToPass} />;
              })()}
            </div>

            {/* LocalSeo section - Always show, pass data directly from API result like SideAudit */}
            <div id="localSEO">
              {(() => {
                // Get the localseo_analysis from the raw API data (same as SideAudit)
                let rawLocalSeoData = data?.result?.localseo_analysis;

                // Parse if it's a JSON string (same logic as parseJsonField)
                if (typeof rawLocalSeoData === "string") {
                  try {
                    rawLocalSeoData = JSON.parse(rawLocalSeoData);
                  } catch (e) {
                    // Failed to parse raw localseo data
                  }
                }

                // Use the raw API data if available, fallback to individual state
                const localSeoDataToPass =
                  rawLocalSeoData || localSeoAnalysis || null;

                return <LocalSeo results={localSeoDataToPass} />;
              })()}
            </div>

            {/* Social section - Always show, pass data directly from API result like SideAudit */}
            <div id="social">
              {(() => {
                // Get the social_analysis from the raw API data (same as SideAudit)
                let rawSocialData = data?.result?.social_analysis;

                // Parse if it's a JSON string (same logic as parseJsonField)
                if (typeof rawSocialData === "string") {
                  try {
                    rawSocialData = JSON.parse(rawSocialData);
                  } catch (e) {
                    // Failed to parse raw social data
                  }
                }

                // Use the raw API data if available, fallback to individual state
                const socialDataToPass =
                  rawSocialData || socialAnalysis || null;

                return <SocialResult results={socialDataToPass} />;
              })()}
            </div>

            {/* Child pages section - Always show, pass data directly from API result like SideAudit */}
            <div id="childPages">
              {(() => {
                // Get the child_pages from the raw API data (same as SideAudit)
                let rawChildPagesData = data?.result?.child_pages;

                // Child pages might be nested in a pages property
                if (rawChildPagesData?.pages) {
                  rawChildPagesData = rawChildPagesData.pages;
                }

                // Use the raw API data if available, fallback to individual state
                const childPagesDataToPass =
                  rawChildPagesData || childPages || null;

                return <ReviewChildPage data={childPagesDataToPass} />;
              })()}
            </div>

            <div className="hidden lg:block">
              <FreeSEOToolbox
                title="Premium SEO Toolbox"
                className="!h-[297px]"
                shapeNum={1}
              />
            </div>
          </div>
        </div>
      </div>
      {loading && (
        <div
          className="w-full fixed top-0 left-0 z-50 bg-white/60 backdrop-blur-md flex-col gap-6 md:gap-8 h-full flex items-center justify-center"
          style={{
            willChange: "opacity, transform",
            transform: "translateZ(0)",
          }}
        >
          <div className="w-full max-w-5xl">
            <DinoLoader />
          </div>
          {showAnalyzingText && (
            <h2 className="text-xl opacity-80 md:text-3xl lg:text-3xl font-black analyzing-text text-center tracking-[0.3em]">
              ANALYSING<span className="dots"></span>
            </h2>
          )}
        </div>
      )}
    </div>
  );
}
