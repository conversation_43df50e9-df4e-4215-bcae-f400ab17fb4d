"use client";

import Image from "next/image";
import Link from "next/link";
import { FooterLink, FooterLinkItem } from "./FooterLink";
import FooterSocialMedia from "./FooterSocialMedia";
import { useEffect, useState } from "react";
import http from "@/services/httpService";
import CookiePreferences from "@/components/cookie-consent/CookiePreferences";

// Define the blog post interface based on the API response
interface BlogPost {
  id: number;
  title: string;
  slug: string;
  url: string;
  category?: {
    name: string;
    slug: string;
  };
}

// Helper function to truncate text
const truncateText = (text: string, maxLength: number = 30): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
};

export default function Footer() {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        setIsLoading(true);
        // Fetch the latest blog posts from the API (without auth)
        const response = await http.get("/api/blog/posts/?limit=4", {
          useAuth: false,
        });

        if (response.data && response.data.results) {
          // Extract the blog posts from the response
          setBlogPosts(response.data.results.slice(0, 4));
        }
      } catch (err) {
        console.error("Error fetching blog posts:", err);
        setError("Failed to load blog posts");
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogPosts();
  }, []);

  return (
    <footer className="w-full mt-8 lg:mt-24 container text-secondary">
      <div className="w-full grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-8 items-start">
        <div className="lg:pr-8 flex-1">
          <div className="w-[100px] h-[51px] relative">
            <Image
              src="/images/appLogo.svg"
              alt="seo analyser logo"
              fill
              quality={100}
              className="w-full h-full object-contain"
              priority
            />
          </div>
          <p className="text-sm lg:text-base text-secondary font-medium my-4">
            {/* Whether you need support, advice, or want to explore how SEOAnalyser
            can help your business grow—reach out anytime. We're here to help,
            Australia-wide. */}
            Empowering Businesses to Rank, Reach, and Grow.
          </p>
          <FooterSocialMedia />
        </div>
        <div className="flex items-start max-[430px]:flex-wrap gap-[50px] lg:gap-13.5 truncate mt-2 lg:mt-0">
          <FooterLink title="Support">
            <FooterLinkItem href="/contactUs" label="Contact Us" />
            <FooterLinkItem href="/aboutUs" label="About Us" />
            {/* <FooterLinkItem href="#" label="Blog" /> */}
            <FooterLinkItem href="/aboutUs#FAQ" label="FAQ" />
          </FooterLink>

          {/* <FooterLink title="Lorem ipsum">
            <FooterLinkItem href="#" label="ipsum" />
            <FooterLinkItem href="#" label="dolor" />
          </FooterLink> */}
        </div>
        <div className="w-full lg:w-auto ">
          {/* <EmailBox /> */}
          <FooterLink title="Blog" href="/blog">
            {isLoading ? (
              // Show loading placeholders
              <>
                <FooterLinkItem href="#" label="Loading..." />
                <FooterLinkItem href="#" label="Loading..." />
                <FooterLinkItem href="#" label="Loading..." />
                <FooterLinkItem href="#" label="Loading..." />
              </>
            ) : error ? (
              // Show error message
              <FooterLinkItem href="/blog" label="Visit our blog" />
            ) : (
              // Show blog posts
              blogPosts.map((post) => (
                <FooterLinkItem
                  key={post.id}
                  href={
                    post.url ||
                    `/blog/${post.category?.slug || "category"}/${post.slug}`
                  }
                  label={truncateText(post.title, 40)}
                />
              ))
            )}
          </FooterLink>
        </div>
      </div>
      <div className="w-full flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 py-6 mt-4 lg:mt-6 border-t border-light-gray">
        <div className="text-sm lg:text-base text-secondary font-medium">
          © 2025 <span className="font-black">SEOANALYSER</span>
        </div>
        <div className="text-sm lg:text-base flex items-center gap-6 text-secondary font-medium">
          <Link
            href="/privacy-policy"
            className="hover:text-primary transition-colors duration-300"
          >
            Privacy Policy
          </Link>
          <Link
            href="/terms-and-conditions"
            className="hover:text-primary transition-colors duration-300"
          >
            Terms & Conditions
          </Link>
          <CookiePreferences className="hover:text-primary transition-colors duration-300" />
        </div>
        {/* <div className="text-sm lg:text-base flex items-center gap-6 text-secondary font-medium">
          <button>SEO Guides</button>
          <button>Resources -Case Studies</button>
        </div> */}
      </div>
    </footer>
  );
}
