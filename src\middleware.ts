import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  // Get the response
  const response = NextResponse.next();

  // Check if we're in development mode
  const isDevelopment = process.env.NODE_ENV === "development";

  // Add security headers
  response.headers.set("X-Frame-Options", "SAMEORIGIN");
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  response.headers.set(
    "Permissions-Policy",
    "camera=(), microphone=(), geolocation=()"
  );

  // Comprehensive CORS headers for all requests
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "X-Requested-With, Content-Type, Authorization, X-Device-ID"
  );
  response.headers.set("Access-Control-Expose-Headers", "X-Device-ID");

  // Handle preflight requests
  if (request.method === "OPTIONS") {
    return new NextResponse(null, { status: 200, headers: response.headers });
  }

  // Comprehensive Content Security Policy - Single source of truth
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.googletagmanager.com https://*.google-analytics.com https://*.google.com https://*.gstatic.com https://static.cloudflareinsights.com https://*.doubleclick.net https://*.hotjar.com https://*.hotjar.io",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "img-src 'self' data: https: http:",
    "font-src 'self' data: https://fonts.gstatic.com",
    "connect-src 'self' https://seoanalyser.com.au https://*.google-analytics.com https://*.google.com https://*.googletagmanager.com https://*.doubleclick.net https://*.gstatic.com https://*.hotjar.com https://*.hotjar.io wss://*.hotjar.com https://api.seoanalyser.com.au",
    "frame-src 'self' https://*.google.com https://*.googletagmanager.com https://*.doubleclick.net",
    "object-src 'none'",
    "base-uri 'self'",
  ].join("; ");

  response.headers.set("Content-Security-Policy", csp);

  // Add Strict-Transport-Security header
  response.headers.set(
    "Strict-Transport-Security",
    "max-age=31536000; includeSubDomains; preload"
  );

  return response;
}

// Apply middleware to all routes
export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
};
