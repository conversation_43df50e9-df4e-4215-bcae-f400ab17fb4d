"use client";
import { ReactNode, useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

type TooltipProps = {
  content: ReactNode;
  children: ReactNode;
  position?: "top" | "right" | "bottom" | "left";
  width?: "sm" | "md" | "lg" | "xl";
  className?: string;
};

const animationVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: { opacity: 0.85, scale: 1 },
  exit: { opacity: 0, scale: 0.9 },
};

/**
 * Responsive tooltip with open/close animations
 */
export default function Tooltip({
  content,
  children,
  position = "top",
  width = "md",
  className = "",
}: TooltipProps) {
  const [visible, setVisible] = useState(false);
  const [pos, setPos] = useState(position);
  const refTrigger = useRef<HTMLDivElement>(null);
  const refTooltip = useRef<HTMLDivElement>(null);

  const widthMap = {
    sm: "min-w-[120px] max-w-xs sm:max-w-sm",
    md: "min-w-[150px] max-w-sm sm:max-w-md",
    lg: "min-w-[180px] max-w-md sm:max-w-lg",
    xl: "min-w-[220px] max-w-lg sm:max-w-xl",
  };

  useEffect(() => {
    if (!visible || !refTooltip.current || !refTrigger.current) return;
    const onUpdate = () => {
      const tip = refTooltip.current!.getBoundingClientRect();
      const trg = refTrigger.current!.getBoundingClientRect();
      const vw = window.innerWidth;
      const vh = window.innerHeight;
      let newPos = position;

      if (position === "left" && trg.left < tip.width + 8) newPos = "right";
      if (position === "right" && trg.right + tip.width + 8 > vw)
        newPos = "left";
      if (position === "top" && trg.top < tip.height + 8) newPos = "bottom";
      if (position === "bottom" && trg.bottom + tip.height + 8 > vh)
        newPos = "top";

      setPos(newPos);
    };
    onUpdate();
    window.addEventListener("resize", onUpdate);
    window.addEventListener("scroll", onUpdate, true);
    return () => {
      window.removeEventListener("resize", onUpdate);
      window.removeEventListener("scroll", onUpdate, true);
    };
  }, [visible, position]);

  const baseClasses =
    "absolute z-50 p-3 rounded-lg bg-gray-800 text-white text-sm shadow-lg" +
    " max-w-[calc(100vw-1rem)] break-words whitespace-normal";

  const positionStyles: Record<string, string> = {
    top: "bottom-full mb-2 left-1/2 transform -translate-x-1/2",
    bottom: "top-full mt-2 left-1/2 transform -translate-x-1/2",
    left: "right-full mr-2 top-1/2 transform -translate-y-1/2",
    right: "left-full ml-2 top-1/2 transform -translate-y-1/2",
  };

  const arrowStyles: Record<string, string> = {
    top: "after:content-[''] after:absolute after:top-full after:left-1/2 after:-translate-x-1/2 after:border-[6px] after:border-t-gray-800 after:border-x-transparent after:border-b-transparent",
    bottom:
      "after:content-[''] after:absolute after:bottom-full after:left-1/2 after:-translate-x-1/2 after:border-[6px] after:border-b-gray-800 after:border-x-transparent after:border-t-transparent",
    left: "after:content-[''] after:absolute after:left-full after:top-1/2 after:-translate-y-1/2 after:border-[6px] after:border-l-gray-800 after:border-y-transparent after:border-r-transparent",
    right:
      "after:content-[''] after:absolute after:right-full after:top-1/2 after:-translate-y-1/2 after:border-[6px] after:border-r-gray-800 after:border-y-transparent after:border-l-transparent",
  };

  return (
    <div className="relative  flex items-center">
      <div
        ref={refTrigger}
        className="inline-flex cursor-pointer"
        onMouseEnter={() => setVisible(true)}
        onMouseLeave={() => setVisible(false)}
        onFocus={() => setVisible(true)}
        onBlur={() => setVisible(false)}
        onClick={() => setVisible((v) => !v)}
        onTouchStart={(e) => {
          e.preventDefault();
          setVisible((v) => !v);
        }}
      >
        {children}
      </div>

      <AnimatePresence>
        {visible && (
          <motion.div
            ref={refTooltip}
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={animationVariants}
            transition={{ duration: 0.15 }}
            className={`
              ${baseClasses}
              ${widthMap[width]}
              ${positionStyles[pos]}
              ${arrowStyles[pos]}
              ${className}
            `}
          >
            {content}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
