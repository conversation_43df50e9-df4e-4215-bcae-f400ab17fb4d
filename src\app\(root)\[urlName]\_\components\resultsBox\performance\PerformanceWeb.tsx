"use client";
import React from "react";
import { PerformanceAnalysis, PageSpeedAnalysis, PageSpeedMobileAnalysis } from "@/types/seoAnalyzerTypes";
import { PerformanceSection } from "../../pdf/PerformanceSection";
import { PageSpeedSection } from "../../pdf/PageSpeedSection";
import BoxPrimary from "../../BoxPrimary";
import ProgressChart from "@/ui/charts/ProgressChart";
import OverallSection from "../OverallSection";

type PerformanceWebProps = {
  results: Partial<PerformanceAnalysis> | null;
  pagespeedData?: Partial<PageSpeedAnalysis> | null;
  pagespeedMobileData?: Partial<PageSpeedMobileAnalysis> | null;
};

export default function PerformanceWeb({ 
  results, 
  pagespeedData, 
  pagespeedMobileData 
}: PerformanceWebProps) {
  // Handle null or undefined results
  if (!results) {
    return (
      <BoxPrimary title="Performance Analysis">
        <div className="w-full flex flex-col items-center gap-4 lg:gap-0 lg:flex-row lg:items-start">
          <ProgressChart
            value="F"
            title="Performance Score"
            size="lg"
            progressStates={[
              { label: "Grade", value: 0, isNoColor: false },
            ]}
          />

          <OverallSection
            title="Performance Analysis"
            description="Analyzing your website's performance metrics..."
          />
        </div>
      </BoxPrimary>
    );
  }

  // Extract the total score and grade - show default values if not available yet
  const totalScore = results?.total_score || { score: 0, grade: "F" };

  // Ensure grade is properly typed for ProgressChart
  const grade =
    (totalScore.grade as
      | "A+"
      | "A"
      | "A-"
      | "B+"
      | "B"
      | "B-"
      | "C+"
      | "C"
      | "C-"
      | "D+"
      | "D"
      | "D-"
      | "F") || "F";

  return (
    <BoxPrimary title="Performance Analysis">
      <div className="w-full flex flex-col items-center gap-4 lg:gap-0 lg:flex-row lg:items-start">
        <ProgressChart
          value={grade}
          title="Performance Score"
          size="lg"
          progressStates={[
            { label: "Grade", value: totalScore.score, isNoColor: false },
          ]}
        />

        <OverallSection
          title={results.overall_title || "Performance Analysis"}
          description={
            results.overall_description ||
            "Analyzing your website's performance metrics..."
          }
        />
      </div>

      {/* Use the new modular PDF components for detailed analysis */}
      <div className="mt-8 space-y-8">
        {/* Performance Analysis */}
        <PerformanceSection
          performanceData={results}
          brand_name="SEO Analyzer"
          brand_website="seoanalyser.com.au"
          brand_photo={null}
          onImageLoad={() => {}}
          onImageError={() => {}}
        />

        {/* PageSpeed Analysis */}
        {(pagespeedData || pagespeedMobileData) && (
          <PageSpeedSection
            pagespeedData={pagespeedData}
            pagespeedMobileData={pagespeedMobileData}
            brand_name="SEO Analyzer"
            brand_website="seoanalyser.com.au"
            brand_photo={null}
            onImageLoad={() => {}}
            onImageError={() => {}}
          />
        )}
      </div>
    </BoxPrimary>
  );
}
