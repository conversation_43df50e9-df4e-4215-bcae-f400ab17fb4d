export default function States({ className }: { className?: string }) {
  const states = [
    {
      description:
        "Run as many audits as you need with SEO Analyser, no limits, no delays.",
      value: "Unlimited SEO Audits",
    },
    {
      description:
        "Generate pro-branded reports with your own identity using our SEO Analyser tool, perfect for agencies and freelancers.",
      value: "White Label Reports",
    },
    {
      description:
        "Get deeper analysis of backlinks and domain status with SEO Analyser to improve authority and rankings.",
      value: "Advanced Backlink & Insights",
    },
    {
      description:
        "From solo site owners to growing agencies, our SEO Analyser makes SEO simple, scalable, and effective for all.",
      value: "Flexible for Everyone",
    },
  ];
  return (
    <div
      className={`w-full container mt-16 sm:mt-20 lg:mt-[154px] ${className}`}
    >
      <div className="bg-white rounded-2xl p-6 hidden lg:flex justify-between">
        {states.map((state, index) => (
          <StateCard
            key={index}
            description={state.description}
            value={state.value}
          />
        ))}
      </div>
      <div className="lg:hidden bg-white rounded-xl sm:rounded-2xl p-3 sm:p-4">
        <div className="flex items-stretch justify-between [&>*:nth-child(1)]:border-r-0 border-b border-light-gray mb-2 sm:mb-3 pb-2 sm:pb-3">
          {states.slice(0, 2).map((state, index) => (
            <StateCard
              key={index}
              description={state.description}
              value={state.value}
            />
          ))}
        </div>
        <div className="flex items-stretch justify-between [&>*:nth-child(1)]:border-r-0">
          {states.slice(2, 4).map((state, index) => (
            <StateCard
              key={index}
              description={state.description}
              value={state.value}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

type StateCardProps = {
  description: string;
  value: string;
};

function StateCard({ description, value }: StateCardProps) {
  return (
    <div className="w-full group flex flex-col px-2 sm:px-3 lg:px-6 py-3 sm:py-4 lg:py-4 border-x border-light-gray first:border-l-0 last:border-r-0">
      {/* Title section with fixed height for alignment */}
      <div className="flex items-center justify-center lg:min-h-[2.2rem] mb-2 lg:mb-3">
        <h2 className="max-[1024px]:group-first:-ml-3 text-base sm:text-lg lg:text-xl font-semibold text-secondary text-center leading-tight max-w-[200px] lg:max-w-[240px]">
          + {value}
        </h2>
      </div>

      {/* Description section - starts at same height for all cards */}
      <div className="flex-1 flex items-start justify-center">
        <p className="text-xs sm:text-sm lg:text-base text-secondary/80 text-center leading-snug max-w-[280px]">
          {description}
        </p>
      </div>
    </div>
  );
}
