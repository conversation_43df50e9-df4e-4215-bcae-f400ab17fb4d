"use client";
import { useState, useRef, useEffect } from "react";
import { useAuthStore } from "@/store/authStore";
import { UserIcon, CrownIcon } from "@/ui/icons/general";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import {
  subscriptionAPI,
  SubscriptionCancellationResponse,
  SubscriptionReactivationResponse,
} from "@/services/httpService";
import SubscriptionCancelModal from "@/components/modals/SubscriptionCancelModal";
import SubscriptionCancelSuccessModal from "@/components/modals/SubscriptionCancelSuccessModal";
import SubscriptionActivateModal from "@/components/modals/SubscriptionActivateModal";
import SubscriptionActivateSuccessModal from "@/components/modals/SubscriptionActivateSuccessModal";

export default function UserProfileDropdown() {
  const { user, logout, checkAuth } = useAuthStore();
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showActivateModal, setShowActivateModal] = useState(false);
  const [showActivateSuccessModal, setShowActivateSuccessModal] =
    useState(false);
  const [cancellationResponse, setCancellationResponse] =
    useState<SubscriptionCancellationResponse | null>(null);
  const [activationResponse, setActivationResponse] =
    useState<SubscriptionReactivationResponse | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Function to handle dropdown toggle with background refresh
  const handleDropdownToggle = async () => {
    if (!isOpen) {
      // Opening dropdown - show cached data immediately, refresh in background
      setIsOpen(true);

      // Background refresh without blocking UI
      try {
        const isValid = await checkAuth(true); // Force refresh in background
        if (!isValid) {
          // If auth check fails, close dropdown and logout user
          console.log("Auth check failed in dropdown, logging out user");
          setIsOpen(false);
          logout();
        }
      } catch (error) {
        console.error("Failed to refresh profile data:", error);
        // On error, also close dropdown and logout for security
        setIsOpen(false);
        logout();
      }
    } else {
      setIsOpen(false);
    }
  };

  // Check if user has active subscription (based on current period)
  const hasActiveSubscription = user?.subscriptions?.some((subscription) => {
    const periodEnd = new Date(subscription.current_period_end);
    return periodEnd > new Date();
  });

  // Get active subscription details
  const activeSubscription = user?.subscriptions?.find((subscription) => {
    const periodEnd = new Date(subscription.current_period_end);
    return periodEnd > new Date();
  });

  // Format price with currency
  const formatPrice = (amount: number, currency: string) => {
    return (
      <>
        {amount.toFixed(2)}{" "}
        <span className="text-xs opacity-75">{currency.toUpperCase()}</span>
      </>
    );
  };

  // Handle subscription cancellation - show confirmation modal
  const handleCancelSubscription = () => {
    setShowCancelModal(true);
  };

  // Confirm subscription cancellation
  const confirmCancelSubscription = async () => {
    if (!activeSubscription) return;

    setIsLoading(true);
    try {
      const response = await subscriptionAPI.cancelSubscription(
        activeSubscription.local_subscription_id ||
          activeSubscription.stripe_subscription_id
      );

      // Store the response for the success modal
      setCancellationResponse(response.data);

      // Force refresh auth to update subscription status
      await checkAuth(true);

      // Show success modal
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      // You might want to show an error toast here
    } finally {
      setIsLoading(false);
    }
  };

  // Handle subscription reactivation - show confirmation modal
  const handleReactivateSubscription = () => {
    setShowActivateModal(true);
  };

  // Confirm subscription reactivation
  const confirmReactivateSubscription = async () => {
    if (!activeSubscription) return;

    setIsLoading(true);
    try {
      const response = await subscriptionAPI.reactivateSubscription(
        activeSubscription.local_subscription_id ||
          activeSubscription.stripe_subscription_id
      );

      // Store the response for the success modal
      setActivationResponse(response.data);

      // Force refresh auth to update subscription status
      await checkAuth(true);

      // Show success modal
      setShowActivateSuccessModal(true);
    } catch (error) {
      console.error("Error reactivating subscription:", error);
      // You might want to show an error toast here
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <motion.button
        onClick={handleDropdownToggle}
        className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 hover:bg-primary/20 relative"
        aria-label="User profile"
        whileHover={{
          scale: 1.05,
        }}
        whileTap={{ scale: 0.95 }}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <UserIcon className="w-7 h-7 text-primary" />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute right-0 mt-2 w-72 bg-white rounded-2xl shadow-lg z-50 border border-gray-200 overflow-hidden "
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          >
            {/* Header with user info */}
            <motion.div
              className="p-4 pb-3"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex flex-col items-center">
                <motion.div
                  className="bg-gray-100 rounded-full w-12 h-12 flex items-center justify-center mb-2"
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  transition={{
                    type: "spring",
                    stiffness: 260,
                    damping: 20,
                    delay: 0.1,
                  }}
                >
                  <UserIcon className="w-7 h-7 text-gray-600" />
                </motion.div>
                <motion.h3
                  className="text-base font-semibold text-gray-800"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                >
                  👋 Hi {user?.first_name || "Alexis Sanchez"}
                </motion.h3>
              </div>
            </motion.div>

            {/* Menu Items */}
            <div className="px-3 py-2 flex flex-col gap-1">
              {/* My Profile */}
              <motion.div
                className="flex items-center gap-2 w-full py-2 px-3 text-gray-800 text-sm font-medium rounded-xl"
                initial={{ opacity: 0, x: -5 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: 0.05 }}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span>
                  {user?.first_name} {user?.last_name}
                </span>
              </motion.div>

              {/* Email Settings */}
              <motion.div
                className="flex items-center gap-2 w-full py-2 px-3 text-gray-800 text-sm font-medium rounded-xl"
                initial={{ opacity: 0, x: -5 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: 0.1 }}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M19.7055 6.79452C18.2618 5.49044 15.8298 5 12 5C8.1702 5 5.73816 5.49044 4.29452 6.79452M19.7055 6.79452C21.0003 7.96413 21.5 9.78823 21.5 12.5C21.5 18.2353 19.2647 20 12 20C4.73529 20 2.5 18.2353 2.5 12.5C2.5 9.78823 2.99972 7.96413 4.29452 6.79452M19.7055 6.79452L13.4142 13.0858C12.6331 13.8668 11.3668 13.8668 10.5858 13.0858L4.29452 6.79452"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span className="truncate">{user?.email}</span>
              </motion.div>

              {/* Subscription Status */}
              <motion.div
                className="py-2 border-b border-gray-300 mb-1"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.12 }}
              >
                <p className="text-xs text-gray-700 font-bold mb-1 px-3">
                  Subscription Status
                </p>
                {hasActiveSubscription && activeSubscription ? (
                  <motion.div className="flex flex-col gap-1 w-full py-3 px-3 bg-primary/10 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CrownIcon className="w-4 h-4 text-primary flex-shrink-0" />
                      <span className="text-sm text-purple-800 font-bold">
                        {activeSubscription.plan_name}
                      </span>
                    </div>
                    <div className="text-xs text-primary/90">
                      {formatPrice(
                        activeSubscription.price_amount,
                        activeSubscription.price_currency
                      )}{" "}
                      / {activeSubscription.price_interval}
                    </div>

                    {/* Only show auto-renew date if auto_renewal is true (or undefined, defaulting to true) */}
                    {activeSubscription.auto_renewal !== false && (
                      <div className="text-xs text-primary/90">
                        Auto-renew:{" "}
                        {new Date(
                          activeSubscription.current_period_end
                        ).toLocaleDateString()}
                      </div>
                    )}
                  </motion.div>
                ) : (
                  <motion.div className="flex items-center gap-2 w-full py-2 px-3 bg-gray-50 rounded-lg">
                    <CrownIcon className="w-4 h-4 text-gray-400 flex-shrink-0" />
                    <span className="text-xs text-gray-500 font-medium">
                      No active subscription
                    </span>
                  </motion.div>
                )}

                {/* Activate Auto Renewal Button - Outside Pro Plan container */}
                {hasActiveSubscription &&
                  activeSubscription &&
                  (activeSubscription.auto_renewal === false ||
                    activeSubscription.cancel_at_period_end) && (
                    <motion.button
                      onClick={handleReactivateSubscription}
                      disabled={isLoading}
                      className="flex items-center justify-center gap-2 w-full py-2 px-3 text-sm font-medium bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mt-2"
                      initial={{ opacity: 0, y: 5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: 0.15 }}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                        />
                      </svg>
                      <span>
                        {isLoading ? "Processing..." : "Activate Auto Renewal"}
                      </span>
                    </motion.button>
                  )}

                {/* Cancel Auto Renewal Button - Outside Pro Plan container */}
                {hasActiveSubscription &&
                  activeSubscription &&
                  activeSubscription.auto_renewal !== false &&
                  !activeSubscription.cancel_at_period_end && (
                    <motion.button
                      onClick={handleCancelSubscription}
                      disabled={isLoading}
                      className="flex items-center justify-center gap-2 w-full py-2 px-3 text-sm font-medium bg-red-100 text-red-600 rounded-lg hover:bg-red-200/85 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mt-2"
                      initial={{ opacity: 0, y: 5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: 0.15 }}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                      <span>
                        {isLoading ? "Processing..." : "Cancel Auto Renewal"}
                      </span>
                    </motion.button>
                  )}
              </motion.div>
              {/* Logout */}
              <motion.button
                onClick={() => {
                  logout();
                  setIsOpen(false);
                }}
                className="flex items-center  text-red-600 gap-2 w-full py-2 px-3 text-sm font-medium rounded-xl hover:bg-red-100 hover:text-red-600"
                whileHover={{
                  color: "#dc2626",
                  x: 2,
                  transition: { duration: 0.2 },
                }}
                initial={{ opacity: 0, x: -5 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: 0.15 }}
                whileTap={{ scale: 0.98 }}
              >
                <svg
                  width={18}
                  height={18}
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 16 16"
                  className="opacity-90"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M4 8h11m0 0-4-4m4 4-4 4m-5 3H3a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h3"
                  ></path>
                </svg>
                <span>Log Out</span>
              </motion.button>
            </div>

            {/* Footer */}
            {/* <motion.div
              className="py-2 px-4 text-center border-t border-gray-100 flex justify-center gap-2 text-xs"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <motion.div>
                <Link
                  href="/pricing"
                  className="text-xs text-gray-500 hover:text-gray-700"
                >
                  Privacy Policy
                </Link>
              </motion.div>
              <span className="text-xs text-gray-400">•</span>
              <motion.div>
                <Link
                  href="/pricing"
                  className="text-xs text-gray-500 hover:text-gray-700"
                >
                  Terms of Service
                </Link>
              </motion.div>
            </motion.div> */}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Subscription Cancel Confirmation Modal */}
      <SubscriptionCancelModal
        isOpen={showCancelModal}
        onClose={() => setShowCancelModal(false)}
        onConfirm={confirmCancelSubscription}
        isLoading={isLoading}
        subscriptionDetails={
          activeSubscription
            ? {
                planName: activeSubscription.plan_name,
                currentPeriodEnd: activeSubscription.current_period_end,
              }
            : undefined
        }
      />

      {/* Subscription Cancel Success Modal */}
      <SubscriptionCancelSuccessModal
        isOpen={showSuccessModal}
        onClose={() => {
          setShowSuccessModal(false);
          setCancellationResponse(null);
        }}
        message={
          cancellationResponse?.message || "Subscription canceled successfully"
        }
        details={cancellationResponse?.details}
        planName={activeSubscription?.plan_name}
      />

      {/* Subscription Activate Confirmation Modal */}
      <SubscriptionActivateModal
        isOpen={showActivateModal}
        onClose={() => setShowActivateModal(false)}
        onConfirm={confirmReactivateSubscription}
        isLoading={isLoading}
        subscriptionDetails={
          activeSubscription
            ? {
                planName: activeSubscription.plan_name,
                currentPeriodEnd: activeSubscription.current_period_end,
              }
            : undefined
        }
      />

      {/* Subscription Activate Success Modal */}
      <SubscriptionActivateSuccessModal
        isOpen={showActivateSuccessModal}
        onClose={() => {
          setShowActivateSuccessModal(false);
          setActivationResponse(null);
        }}
        message={
          activationResponse?.message || "Auto-renewal activated successfully"
        }
        details={
          activeSubscription && activeSubscription.current_period_end
            ? {
                current_period_end: activeSubscription.current_period_end,
                cancel_at_period_end: false, // Since we just reactivated
                will_auto_renew: activeSubscription.auto_renewal !== false,
              }
            : activationResponse?.details
        }
        planName={activeSubscription?.plan_name}
      />
    </div>
  );
}
