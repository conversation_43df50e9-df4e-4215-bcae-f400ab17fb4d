"use client";

const BlogCardSkeleton: React.FC = () => {
  return (
    <div className="flex flex-col h-full min-h-[400px] sm:min-h-[420px] md:min-h-[450px] rounded-lg p-2 sm:p-3">
      <style jsx>{`
        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }
        .shimmer {
          background: linear-gradient(
            90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%
          );
          background-size: 200% 100%;
          animation: shimmer 2s infinite linear;
        }
      `}</style>
      <div>
        {/* Image skeleton */}
        <div className="mb-2 overflow-hidden rounded-lg relative w-full h-[200px] sm:h-[220px] md:h-[240px] lg:h-[260px] shimmer">
          {/* Date holder skeleton */}
          <div className="p-3 sm:p-4 md:p-6 absolute top-0 left-0">
            <div className="bg-white/80 rounded-lg px-2 py-1 w-16 h-8"></div>
          </div>
        </div>

        {/* Author and category skeleton */}
        <div className="text-[10px] sm:text-[11px] md:text-[12px] flex flex-wrap items-center mb-2">
          <div className="h-3 shimmer rounded w-20"></div>
          <div className="ml-1 sm:ml-2 h-3 shimmer rounded w-16"></div>
        </div>
      </div>

      <div className="flex flex-col flex-1 justify-between">
        <div className="flex-1">
          {/* Title skeleton */}
          <div className="mb-2 space-y-2">
            <div className="h-5 sm:h-6 md:h-7 shimmer rounded w-full"></div>
            <div className="h-5 sm:h-6 md:h-7 shimmer rounded w-3/4"></div>
          </div>

          {/* Description skeleton */}
          <div className="space-y-2">
            <div className="h-3 sm:h-4 md:h-5 shimmer rounded w-full"></div>
            <div className="h-3 sm:h-4 md:h-5 shimmer rounded w-5/6"></div>
            <div className="h-3 sm:h-4 md:h-5 shimmer rounded w-4/5 md:block hidden"></div>
          </div>
        </div>

        {/* Tags skeleton - Fixed at bottom */}
        <div className="mt-3 min-h-[28px] flex items-end">
          <div className="flex flex-wrap gap-1">
            <div className="h-6 shimmer rounded-full w-12"></div>
            <div className="h-6 shimmer rounded-full w-16"></div>
            <div className="h-6 shimmer rounded-full w-14"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogCardSkeleton;
