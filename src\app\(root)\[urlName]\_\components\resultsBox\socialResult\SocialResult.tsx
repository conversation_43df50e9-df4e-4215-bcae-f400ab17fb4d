import React from "react";
import BoxPrimary from "../../BoxPrimary";
import <PERSON><PERSON><PERSON> from "@/ui/charts/ProgressChart";
import PageLinked from "./PageLinked";
import {
  FacebookIcon,
  LinkedinIcon,
  TelegramIcon,
  XIcon,
  YouTubeIcon,
} from "@/ui/icons/socialMedia";
import { InstagramColorIcon } from "@/ui/icons/socialMedia/InstagramColorIcon";
import ShowMoreSection from "../usability/ShowMoreSection";
import { DocumentCheckIcon, DocumentCrossIcon } from "@/ui/icons/general";
import {
  SocialAnalysis as BaseSocialAnalysis,
  SocialPlatformAnalysis as BaseSocialPlatformAnalysis,
  YouTubePlatformAnalysis as BaseYouTubePlatformAnalysis,
} from "@/types/seoAnalyzerTypes";
import LoadingSlate from "@/components/loading/LoadingSlate";
import OverallSection from "../OverallSection";

// Extended types to handle additional properties used in the component
interface ExtendedSocialPlatformAnalysis extends BaseSocialPlatformAnalysis {
  og_tags?: Record<string, string>;
  twitter_cards?: Record<string, string>;
}

// Extended type for Telegram to handle channel_url property
interface ExtendedTelegramPlatformAnalysis extends BaseSocialPlatformAnalysis {
  channel_url?: string;
}

// Using type alias instead of empty interface
type ExtendedYouTubePlatformAnalysis = BaseYouTubePlatformAnalysis;

// Extended SocialAnalysis to use our extended platform types
interface SocialAnalysis extends BaseSocialAnalysis {
  facebook: ExtendedSocialPlatformAnalysis;
  twitter: ExtendedSocialPlatformAnalysis;
  instagram: ExtendedSocialPlatformAnalysis;
  linkedin: ExtendedSocialPlatformAnalysis;
  youtube: ExtendedYouTubePlatformAnalysis;
  telegram: ExtendedTelegramPlatformAnalysis;
}

type Props = {
  results:
    | SocialAnalysis
    | {
        social_analysis: SocialAnalysis;
      }
    | string
    | null;
};

export default function SocialResult({ results }: Props) {
  // Optimized data handling - show content immediately when any data is available
  const hasAnyData =
    results &&
    typeof results === "object" &&
    results !== null &&
    Object.keys(results).length > 0;

  // Only show loading state if we have no data at all
  if (!hasAnyData) {
    return (
      <BoxPrimary title="Social Media share buttons">
        <LoadingSlate
          title="Loading social results..."
          showHeader={true}
          showCards={false}
          showChart={true}
          showProgress={true}
          height="lg"
        />
      </BoxPrimary>
    );
  }

  // Determine if we have the social_analysis property or if results is already the social_analysis object
  const social_analysis =
    "social_analysis" in results ? results.social_analysis : results;

  // Check if we have header data
  const hasHeaderData =
    social_analysis.overall_title || social_analysis.overall_description;

  // Get the total score and grade from the data
  const totalScore = social_analysis.total_score?.score || 0;
  const grade = (social_analysis.total_score?.grade || "F") as
    | "A+"
    | "A"
    | "A-"
    | "B+"
    | "B"
    | "B-"
    | "C+"
    | "C"
    | "C-"
    | "D+"
    | "D"
    | "D-"
    | "F";

  // Determine social status message based on grade (commented out as it's not currently used)
  // const socialStatusMessage =
  //   grade.startsWith("A") || grade.startsWith("B")
  //     ? "Your Social is good"
  //     : "Your Social needs improvement";

  // Helper function to get recommendation text from either string or object format
  // Currently unused but may be needed in the future
  // const getRecommendationText = (
  //   recommendation: string | RecommendationType
  // ): string => {
  //   if (
  //     typeof recommendation === "object" &&
  //     recommendation !== null &&
  //     "text" in recommendation
  //   ) {
  //     return recommendation.text;
  //   }
  //   return recommendation as string;
  // };

  // Helper function to check if a platform is available (has pass property set to true)
  const isPlatformAvailable = (
    platform: { pass?: boolean } | null | undefined
  ): boolean => {
    if (!platform) return false;
    // In the new data structure, 'pass' is used instead of 'status'
    return platform.pass === true;
  };

  return (
    <BoxPrimary title="Social ">
      <div className="w-full flex flex-col items-center gap-6 lg:flex-row lg:items-start mb-4 lg:mb-6">
        <ProgressChart
          value={grade}
          title="Social Media Score"
          size="lg"
          progressStates={[
            { label: "Grade", value: totalScore, isNoColor: true },
            { label: "Total", value: 100 },
          ]}
        />

        {hasHeaderData ? (
          <OverallSection
            title={social_analysis.overall_title || "Social Media Analysis"}
            description={
              social_analysis.overall_description ||
              "Analyzing your website's social media presence and integration..."
            }
          />
        ) : (
          <div className="flex-1 p-6 rounded-lg bg-primary/10 animate-pulse">
            <div className="h-5 bg-primary/20 rounded w-3/4 mb-3"></div>
            <div className="h-4 bg-primary/15 rounded w-full mb-2"></div>
            <div className="h-4 bg-primary/15 rounded w-5/6"></div>
          </div>
        )}
      </div>

      <div className="flex flex-col gap-8 mt-8 lg:gap-10">
        {/* Facebook */}
        <PageLinked
          title="Facebook Page Linked"
          desscription={
            social_analysis.facebook?.description || "Facebook analysis"
          }
          pageLink={
            social_analysis.facebook?.page_url !== "Not Found"
              ? social_analysis.facebook?.page_url || ""
              : ""
          }
          pageIcon={
            isPlatformAvailable(social_analysis.facebook) ? (
              <FacebookIcon className="w-6 h-6 text-[#1877F2]" />
            ) : undefined
          }
        >
          <ShowMoreSection
            title="Facebook Presence"
            description={`Facebook is the largest social network with over 2.8 billion users. ${
              isPlatformAvailable(social_analysis.facebook)
                ? "Your website has a Facebook presence."
                : "No Facebook presence detected."
            }`}
            passed={isPlatformAvailable(social_analysis.facebook)}
            icon={
              isPlatformAvailable(social_analysis.facebook) ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={social_analysis.facebook?.importance || "Medium"}
            recommendation={
              social_analysis.facebook?.recommendation || {
                text: "Add Facebook integration",
                priority: "Medium",
              }
            }
          />
          {social_analysis.facebook?.og_tags && (
            <ShowMoreSection
              title="Facebook Open Graph Tags"
              description={`Open Graph tags control how your content appears when shared on Facebook. ${
                Object.keys(social_analysis.facebook?.og_tags || {}).length > 0
                  ? "Found " +
                    Object.keys(social_analysis.facebook?.og_tags || {})
                      .length +
                    " OG tags."
                  : "No OG tags found."
              }`}
              passed={
                Object.keys(social_analysis.facebook?.og_tags || {}).length > 0
              }
              icon={
                Object.keys(social_analysis.facebook?.og_tags || {}).length >
                0 ? (
                  <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
                ) : (
                  <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
                )
              }
              importance={social_analysis.facebook?.importance || "Medium"}
              recommendation={
                social_analysis.facebook?.recommendation || {
                  text: "Add Facebook integration",
                  priority: "Medium",
                }
              }
            >
              {Object.keys(social_analysis.facebook?.og_tags || {}).length >
                0 && (
                <div className="max-h-80 overflow-y-auto pr-1 sm:pr-2">
                  {Object.entries(social_analysis.facebook?.og_tags || {}).map(
                    ([key, value]) => (
                      <p
                        key={key}
                        className="text-xs sm:text-sm text-secondary/80 mb-1 sm:mb-2"
                      >
                        <strong>{key}:</strong> {String(value)}
                      </p>
                    )
                  )}
                </div>
              )}
            </ShowMoreSection>
          )}
          <ShowMoreSection
            title="Facebook Pixel"
            description={`Facebook Pixel helps track conversions from Facebook ads and optimise campaigns. ${
              social_analysis.facebook?.pixel_id
                ? "Pixel ID detected."
                : "No Pixel ID found."
            }`}
            passed={!!social_analysis.facebook?.pixel_id}
            icon={
              !!social_analysis.facebook?.pixel_id ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={social_analysis.facebook?.importance || "Medium"}
            recommendation={
              social_analysis.facebook?.recommendation || {
                text: "Add Facebook Pixel",
                priority: "Medium",
              }
            }
          />
        </PageLinked>

        {/* Twitter/X */}
        <PageLinked
          title="Twitter/X Page Linked"
          desscription={
            social_analysis.twitter?.description || "Twitter/X analysis"
          }
          pageLink={
            social_analysis.twitter?.profile_url !== "Not Found"
              ? social_analysis.twitter?.profile_url || ""
              : ""
          }
          pageIcon={<XIcon className="w-6 h-6 text-black" />}
        >
          <ShowMoreSection
            title="Twitter/X Presence"
            description={`Twitter (X) is a real-time information network with over 350 million users. ${
              isPlatformAvailable(social_analysis.twitter)
                ? "Your website has a Twitter/X presence."
                : "No Twitter/X presence detected."
            }`}
            passed={isPlatformAvailable(social_analysis.twitter)}
            icon={
              isPlatformAvailable(social_analysis.twitter) ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={social_analysis.twitter?.importance || "Medium"}
            recommendation={
              social_analysis.twitter?.recommendation || {
                text: "Add Twitter/X presence",
                priority: "Medium",
              }
            }
          />
          {social_analysis.twitter?.twitter_cards && (
            <ShowMoreSection
              title="Twitter Cards"
              description={`Twitter Cards enhance tweets with additional content. ${
                Object.keys(social_analysis.twitter?.twitter_cards || {})
                  .length > 0
                  ? "Found Twitter Cards."
                  : "No Twitter Cards found."
              }`}
              passed={
                Object.keys(social_analysis.twitter?.twitter_cards || {})
                  .length > 0
              }
              icon={
                Object.keys(social_analysis.twitter?.twitter_cards || {})
                  .length > 0 ? (
                  <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
                ) : (
                  <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
                )
              }
              importance={social_analysis.twitter?.importance || "Medium"}
              recommendation={
                social_analysis.twitter?.recommendation || {
                  text: "Add Twitter Cards",
                  priority: "Medium",
                }
              }
            >
              {Object.keys(social_analysis.twitter?.twitter_cards || {})
                .length > 0 && (
                <div className="max-h-80 overflow-y-auto pr-1 sm:pr-2">
                  {Object.entries(
                    social_analysis.twitter?.twitter_cards || {}
                  ).map(([key, value]) => (
                    <p
                      key={key}
                      className="text-xs sm:text-sm text-secondary/80 mb-1 sm:mb-2"
                    >
                      <strong>{key}:</strong> {String(value)}
                    </p>
                  ))}
                </div>
              )}
            </ShowMoreSection>
          )}
        </PageLinked>

        {/* Instagram */}
        <PageLinked
          title="Instagram Page Linked"
          desscription={
            social_analysis.instagram?.description || "Instagram analysis"
          }
          pageLink={
            social_analysis.instagram?.profile_url !== "Not Found"
              ? social_analysis.instagram?.profile_url || ""
              : ""
          }
          pageIcon={<InstagramColorIcon className="w-6 h-6" />}
        >
          <ShowMoreSection
            title="Instagram Presence"
            description={`Instagram is a visual-focused platform with over 1 billion users. ${
              isPlatformAvailable(social_analysis.instagram)
                ? "Your website has an Instagram presence."
                : "No Instagram presence detected."
            }`}
            passed={isPlatformAvailable(social_analysis.instagram)}
            icon={
              isPlatformAvailable(social_analysis.instagram) ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={social_analysis.instagram?.importance || "Medium"}
            recommendation={
              social_analysis.instagram?.recommendation || {
                text: "Add Instagram presence",
                priority: "Medium",
              }
            }
          />
        </PageLinked>

        {/* LinkedIn */}
        <PageLinked
          title="LinkedIn Page Linked"
          desscription={
            social_analysis.linkedin?.description || "LinkedIn analysis"
          }
          pageLink={
            social_analysis.linkedin?.profile_url !== "Not Found"
              ? social_analysis.linkedin?.profile_url || ""
              : ""
          }
          pageIcon={<LinkedinIcon className="w-6 h-6 text-[#0A66C2]" />}
        >
          <ShowMoreSection
            title="LinkedIn Presence"
            description={`LinkedIn is a professional networking platform with over 722 million users. ${
              isPlatformAvailable(social_analysis.linkedin)
                ? "Your website has a LinkedIn presence."
                : "No LinkedIn presence detected."
            }`}
            passed={isPlatformAvailable(social_analysis.linkedin)}
            icon={
              isPlatformAvailable(social_analysis.linkedin) ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={social_analysis.linkedin?.importance || "Medium"}
            recommendation={
              social_analysis.linkedin?.recommendation || {
                text: "Add LinkedIn presence",
                priority: "Medium",
              }
            }
          />
        </PageLinked>

        {/* YouTube */}
        <PageLinked
          title="YouTube Channel"
          desscription={
            social_analysis.youtube?.description || "YouTube analysis"
          }
          pageLink={
            social_analysis.youtube?.channel_url !== "Not Found"
              ? social_analysis.youtube?.channel_url || ""
              : ""
          }
          pageIcon={<YouTubeIcon className="w-6 h-6 text-[#FF0000]" />}
        >
          <ShowMoreSection
            title="YouTube Presence"
            description={`YouTube is the second largest search engine with over 2 billion users. ${
              isPlatformAvailable(social_analysis.youtube)
                ? "Your website has a YouTube presence."
                : "No YouTube channel detected."
            }`}
            passed={isPlatformAvailable(social_analysis.youtube)}
            icon={
              isPlatformAvailable(social_analysis.youtube) ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={social_analysis.youtube?.importance || "Medium"}
            recommendation={
              social_analysis.youtube?.recommendation || {
                text: "Add YouTube presence",
                priority: "Medium",
              }
            }
          />
          {isPlatformAvailable(social_analysis.youtube) &&
            social_analysis.youtube?.statistics && (
              <ShowMoreSection
                title="YouTube Channel Activity"
                description={`Channel statistics: ${
                  social_analysis.youtube.statistics.subscriberCount || 0
                } subscribers, ${
                  social_analysis.youtube.statistics.videoCount || 0
                } videos, ${
                  social_analysis.youtube.statistics.viewCount || 0
                } views.`}
                passed={
                  (social_analysis.youtube.statistics.videoCount || 0) > 0
                }
                icon={
                  (social_analysis.youtube.statistics.videoCount || 0) > 0 ? (
                    <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
                  ) : (
                    <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
                  )
                }
                importance={social_analysis.youtube?.importance || "Medium"}
                recommendation={
                  social_analysis.youtube?.recommendation || {
                    text: "Add YouTube content",
                    priority: "Medium",
                  }
                }
              />
            )}
        </PageLinked>

        {/* Telegram */}
        <PageLinked
          title="Telegram Channel"
          desscription={
            social_analysis.telegram?.description || "Telegram channel analysis"
          }
          pageLink={
            social_analysis.telegram?.channel_url !== "Not Found"
              ? social_analysis.telegram?.channel_url || ""
              : ""
          }
          pageIcon={
            <TelegramIcon className="w-6 h-6 text-white bg-blue-500 rounded-full p-1" />
          }
        >
          <ShowMoreSection
            title="Telegram Presence"
            description={`Telegram is a messaging platform popular for channels and group communication. ${
              isPlatformAvailable(social_analysis.telegram)
                ? "Your website has a Telegram presence."
                : "No Telegram channel detected."
            }`}
            passed={isPlatformAvailable(social_analysis.telegram)}
            icon={
              isPlatformAvailable(social_analysis.telegram) ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={social_analysis.telegram?.importance || "Medium"}
            recommendation={
              social_analysis.telegram?.recommendation || {
                text: "Consider adding Telegram presence",
                priority: "Low",
              }
            }
          />
        </PageLinked>

        {/* Social Meta Tags */}
        <ShowMoreSection
          title="Social Meta Tags"
          description={
            social_analysis.social_meta_tags?.description ||
            "Social meta tags analysis"
          }
          passed={isPlatformAvailable(social_analysis.social_meta_tags)}
          icon={
            isPlatformAvailable(social_analysis.social_meta_tags) ? (
              <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
            ) : (
              <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
            )
          }
          importance={social_analysis.social_meta_tags?.importance || "Medium"}
          recommendation={
            social_analysis.social_meta_tags?.recommendation || {
              text: "Add social meta tags",
              priority: "Medium",
            }
          }
        >
          <div className="max-h-80 overflow-y-auto pr-1 sm:pr-2">
            <div className="mb-3 sm:mb-4">
              <h6 className="font-semibold text-secondary mb-1 sm:mb-2">
                Open Graph Tags (
                {social_analysis.social_meta_tags?.og_tags_count || 0})
              </h6>
              {Object.keys(social_analysis.social_meta_tags?.og_tags || {})
                .length > 0 ? (
                Object.entries(
                  social_analysis.social_meta_tags?.og_tags || {}
                ).map(([key, value]) => (
                  <p
                    key={key}
                    className="text-xs sm:text-sm text-secondary/80 mb-1 sm:mb-2"
                  >
                    <strong>{key}:</strong> {value}
                  </p>
                ))
              ) : (
                <p className="text-xs sm:text-sm text-secondary/80">
                  No Open Graph tags found.
                </p>
              )}
            </div>
            <div>
              <h6 className="font-semibold text-secondary mb-1 sm:mb-2">
                Twitter Tags (
                {social_analysis.social_meta_tags?.twitter_tags_count || 0})
              </h6>
              {Object.keys(social_analysis.social_meta_tags?.twitter_tags || {})
                .length > 0 ? (
                Object.entries(
                  social_analysis.social_meta_tags?.twitter_tags || {}
                ).map(([key, value]) => (
                  <p
                    key={key}
                    className="text-xs sm:text-sm text-secondary/80 mb-1 sm:mb-2"
                  >
                    <strong>{key}:</strong> {value}
                  </p>
                ))
              ) : (
                <p className="text-xs sm:text-sm text-secondary/80">
                  No Twitter tags found.
                </p>
              )}
              {social_analysis.social_meta_tags?.missing_twitter_tags &&
                social_analysis.social_meta_tags.missing_twitter_tags.length >
                  0 && (
                  <div className="mt-1 sm:mt-2">
                    <p className="text-xs sm:text-sm text-secondary/80 font-semibold">
                      Missing Twitter Tags:
                    </p>
                    <ul className="list-disc pl-4 sm:pl-5">
                      {social_analysis.social_meta_tags.missing_twitter_tags.map(
                        (tag) => (
                          <li
                            key={tag}
                            className="text-xs sm:text-sm text-secondary/80"
                          >
                            {tag}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
            </div>
          </div>
        </ShowMoreSection>

        {/* Share Buttons */}
        <ShowMoreSection
          title="Social Media Share Buttons"
          description={
            social_analysis.share_buttons?.description ||
            "Social share buttons analysis"
          }
          passed={isPlatformAvailable(social_analysis.share_buttons)}
          icon={
            isPlatformAvailable(social_analysis.share_buttons) ? (
              <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
            ) : (
              <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
            )
          }
          importance={social_analysis.share_buttons?.importance || "Medium"}
          recommendation={
            social_analysis.share_buttons?.recommendation || {
              text: "Add social share buttons",
              priority: "Low",
            }
          }
        >
          <div className="max-h-80 overflow-y-auto pr-1 sm:pr-2">
            <p className="text-xs sm:text-sm text-secondary/80 mb-1 sm:mb-2">
              <strong>Share Buttons Found:</strong>{" "}
              {social_analysis.share_buttons?.button_count || 0}
            </p>
            <p className="text-xs sm:text-sm text-secondary/80 mb-1 sm:mb-2">
              <strong>&quot;AddThis&quot; Detected:</strong>{" "}
              {social_analysis.share_buttons?.addthis_detected ? "Yes" : "No"}
            </p>
            <p className="text-xs sm:text-sm text-secondary/80 mb-1 sm:mb-2">
              <strong>&quot;ShareThis&quot; Detected:</strong>{" "}
              {social_analysis.share_buttons?.sharethis_detected ? "Yes" : "No"}
            </p>
          </div>
        </ShowMoreSection>
      </div>
    </BoxPrimary>
  );
}
