"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { usePara<PERSON>, useSearchParams } from "next/navigation";
import { useBlogStore } from "@/store/blogStore";
import { BlogCardProps } from "../components/BlogCard";
import BlogCard from "../components/BlogCard";
import SearchInput from "../components/SearchInput";
import CategoryList from "../components/CategoryList";
import { Pagination } from "../components/Pagination";
import BannerScroll from "../components/BannerScroll";
import BlogCardSkeleton from "../components/BlogCardSkeleton";
import BannerScrollSkeleton from "../components/BannerScrollSkeleton";
import CategoryHeaderSkeleton from "../components/CategoryHeaderSkeleton";
import http from "@/services/httpService";

interface BlogPostResult {
  id: number;
  title: string;
  slug: string;
  author: {
    id: number;
    email: string;
    display_name: string;
  };
  body: string;
  publish_timestamp: number;
  status: string;
  snippet: string;
  cover_image: string | null;
  url: string;
  tags: string[];
  similar_posts: Array<{
    id: number;
    title: string;
    slug: string;
    author: {
      id: number;
      email: string;
      display_name: string;
    };
    publish_timestamp: number;
    tags: string[];
  }>;
}

interface BlogApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BlogPostResult[];
  categories: Category[];
}

interface Category {
  name: string;
  slug: string;
}

// Number of posts per page
const POSTS_PER_PAGE = 10;

// The main page component
export default function CategoryPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const [blogPosts, setBlogPosts] = useState<BlogCardProps[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [categoryName, setCategoryName] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [nextPageUrl, setNextPageUrl] = useState<string | null>(null);
  const [previousPageUrl, setPreviousPageUrl] = useState<string | null>(null);
  const [selectedTag, setSelectedTag] = useState<string | null>(null);

  const categorySlug = params?.category as string;
  const pageParam = searchParams?.get("page") || "1";

  // Fetch data on component mount and when category/page changes
  useEffect(() => {
    const fetchData = async () => {
      if (!categorySlug) return;

      setLoading(true);
      setError(null);

      try {
        const currentPageNum = Number(pageParam) || 1;
        setCurrentPage(currentPageNum);

        // Ensure current page is valid
        if (isNaN(currentPageNum) || currentPageNum < 1) {
          window.location.href = `/blog/${categorySlug}`;
          return;
        }

        // Fetch posts by category using the API endpoint with pagination (without auth)
        const response = await http.get(
          `/api/blog/category/${categorySlug}/?${
            currentPageNum > 1 ? `page=${currentPageNum}` : ""
          }`,
          { useAuth: false }
        );

        const data: BlogApiResponse = response.data;
        const catName =
          data.categories.find((cat: Category) => cat.slug === categorySlug)
            ?.name || decodeURIComponent(categorySlug);

        // Calculate total pages
        const totalPagesCalc = Math.ceil(data.count / POSTS_PER_PAGE);

        // Map API data to BlogCardProps format
        const posts: BlogCardProps[] = data.results.map((post) => ({
          id: post.id,
          title: post.title,
          slug: post.slug,
          author: post.author,
          snippet: post.snippet || "",
          publish_timestamp: post.publish_timestamp,
          url: post.url,
          tags: post.tags,
          cover_image: post.cover_image,
          category: {
            name: catName,
            slug: categorySlug,
          },
        }));

        setBlogPosts(posts);
        setCategories(data.categories);
        setCategoryName(catName);
        setTotalPages(totalPagesCalc);
        setNextPageUrl(data.next);
        setPreviousPageUrl(data.previous);
      } catch (err) {
        console.error("Error fetching category posts:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load category posts"
        );

        // Attempt to fetch categories for the error state (without auth)
        try {
          const catResponse = await http.get("/api/blog/categories/", {
            useAuth: false,
          });
          setCategories(catResponse.data.categories);
        } catch (e) {
          console.error("Error fetching categories:", e);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [categorySlug, pageParam]);

  const handleTagClick = (tag: string) => {
    // Toggle tag selection within the current category page
    if (selectedTag === tag) {
      setSelectedTag(null); // Clear tag filter if clicking the same tag
    } else {
      setSelectedTag(tag); // Set new tag filter
    }
  };

  return (
    <div className="w-full mt-8 lg:mt-[84px] mb-12 container">
      <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1 flex flex-col gap-4">
          <SearchInput />
          <CategoryList
            categories={categories}
            currentCategory={categorySlug}
          />
        </div>

        {/* Main content */}
        <div className="lg:col-span-3">
          {/* Loading State */}
          {loading && (
            <>
              {/* Category header skeleton */}
              <CategoryHeaderSkeleton />

              {/* Banner skeleton */}
              <BannerScrollSkeleton />

              {/* Blog cards skeleton */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6 auto-rows-fr">
                {Array.from({ length: 6 }).map((_, index) => (
                  <BlogCardSkeleton key={index} />
                ))}
              </div>
            </>
          )}

          {/* Error State */}
          {error && !loading && (
            <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
              <h2 className="text-xl font-bold text-red-700 mb-2">
                Error Loading Category
              </h2>
              <p className="text-red-600 mb-4">
                We're having trouble loading the blog posts for this category.
                Please try again later.
              </p>
              <Link href="/blog" className="text-primary hover:underline">
                ← Back to Blog
              </Link>
            </div>
          )}

          {/* Content - show when not loading and no error */}
          {!loading && !error && (
            <>
              <div className="mb-6">
                <div className="flex flex-wrap gap-2 mb-4">
                  <Link
                    href="/blog"
                    className="text-primary hover:underline inline-block"
                  >
                    Blog
                  </Link>
                  <span className="text-gray-400">/</span>
                  <span className="text-gray-600 capitalize">
                    {categoryName}
                  </span>
                </div>
                <h1 className="text-secondary font-black text-2xl lg:text-4xl mt-2">
                  {categoryName}
                </h1>
                <p className="text-gray-600 mt-2">
                  Browse all blog posts in the {categoryName} category.
                </p>
              </div>

              {/* Banner for featured posts - only show if we have enough posts */}
              {blogPosts.length >= 3 && (
                <BannerScroll blogData={blogPosts.slice(0, 3)} />
              )}

              {/* Blog posts grid */}
              {blogPosts.length > 0 ? (
                <>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6 auto-rows-fr">
                    {blogPosts.map((post) => (
                      <BlogCard
                        key={post.id}
                        {...post}
                        onTagClick={handleTagClick}
                      />
                    ))}
                  </div>

                  {totalPages > 1 && (
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      nextPageUrl={nextPageUrl}
                      previousPageUrl={previousPageUrl}
                    />
                  )}
                </>
              ) : (
                <div className="p-6 bg-gray-50 border border-gray-200 rounded-lg text-center">
                  <h2 className="text-xl font-bold text-gray-700 mb-2">
                    No Posts Found in This Category
                  </h2>
                  <p className="text-gray-600">
                    There are currently no blog posts available in the{" "}
                    {categoryName} category. Please check back later or browse
                    other categories.
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
