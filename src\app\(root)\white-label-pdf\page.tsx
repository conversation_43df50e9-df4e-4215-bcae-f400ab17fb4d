import React, { Suspense } from "react";
import WhiteLabelPdfClient from "./WhiteLabelPdfClient";

export default function WhiteLabelPdfPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <div className="mt-8">
        <WhiteLabelPdfClient />
      </div>
    </Suspense>
  );
}

function LoadingFallback() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen py-20">
      <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      <p className="mt-4 text-gray-600">Loading...</p>
    </div>
  );
}
