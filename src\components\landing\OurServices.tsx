import { FolderCheckIcon } from "@/ui/icons/general";
import Title from "./Title";
import { ArrowRigthIcon } from "@/ui/icons/navigation";
import Link from "next/link";

type Service = {
  title: string;
  description: string;
  blogUrl: string;
  blogTitle: string;
};

export default function OurServices() {
  const services: Service[] = [
    {
      title: "Keyword Research & Analysis",
      description: "Discover the most profitable keywords for your business.",
      blogUrl:
        "/blog/services/mastering-keyword-research-find-the-phrases-that-drive-traffic",
      blogTitle: "Mastering Keyword Research",
    },
    {
      title: "On-Page SEO Optimisation",
      description: "Improve your website’s structure, speed, and content.",
      blogUrl: "/blog/services/optimizing-website-content-for-search-engines",
      blogTitle: "Optimizing Website Content for Search Engines",
    },
    {
      title: "Technology Review",
      description:
        "Fix crawling, indexing, and performance issues that block search engine visibility.",
      blogUrl: "/blog/services/comprehensive-guide-to-technical-seo",
      blogTitle: "Comprehensive Guide to Technical SEO",
    },
    {
      title: "Backlink Building",
      description:
        "Increase your site’s authority with high-quality backlinks.",
      blogUrl:
        "/blog/services/advanced-link-building-strategies-from-outreach-to-pr",
      blogTitle: "Advanced Link Building Strategies",
    },
    {
      title: "Analytics & Reporting",
      description: "Data-driven insights and practical recommendations.",
      blogUrl:
        "/blog/services/data-driven-seo-analytics-reporting-practical-recommendations",
      blogTitle: "Data-Driven SEO Analytics & Reporting",
    },
    {
      title: "Local SEO",
      description:
        "Optimise for location-based searches and get found by nearby customers.",
      blogUrl:
        "/blog/services/what-is-local-seo-why-its-a-game-changer-for-businesses",
      blogTitle: "What Is Local SEO & Why It's a Game Changer",
    },
    {
      title: "Performance SEO",
      description:
        "Optimise speed, usability, and mobile experience to meet core SEO standards.",
      blogUrl:
        "/blog/services/core-web-vitals-understanding-and-improving-your-scores",
      blogTitle: "Core Web Vitals: Understanding and Improving Your Scores",
    },
    {
      title: "SEO Audit & Consultation",
      description:
        "Get a full-site audit with expert insights and recommendations.",
      blogUrl: "/blog/services/how-to-choose-the-right-seo-agency",
      blogTitle: "How to Choose the Right SEO Agency",
    },
  ];

  return (
    <div className="w-full container max-w-full__customeLG mt-8 lg:mt-[84px]">
      <div className="flex items-center justify-between lg:justify-center">
        <Title title="Our Services" />
        <div className="flex lg:hidden items-center gap-1 text-primary text-sm">
          <div>
            <ArrowRigthIcon className="w-8 h-8" />
          </div>
        </div>
      </div>
      <div className="w-full grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-6 my-4">
        {services.map((item, index) => (
          <ServiceCard
            key={index}
            title={item.title}
            description={item.description}
            blogUrl={item.blogUrl}
            blogTitle={item.blogTitle}
            icon={<FolderCheckIcon />}
          />
        ))}
      </div>
      <div className="w-full hidden lg:flex justify-end"></div>
    </div>
  );
}

type ServiceCardProps = {
  title: string;
  description: string;
  blogUrl: string;
  blogTitle: string;
  icon: React.ReactNode;
};

function ServiceCard({
  title,
  description,
  blogUrl,
  blogTitle,
  icon,
}: ServiceCardProps) {
  return (
    <div className="w-full flex flex-col justify-between bg-white rounded-lg  p-6 hover:shadow-[0_0_25px_-5px_rgba(0,0,0,0.25)] duration-300 ease-in-out">
      <div>
        <div className="flex items-center gap-2 text-secondary">
          {icon}
          <h4 className="font-semibold">{title}</h4>
        </div>
        <p className="text-sm text-secondary/80 mt-2">{description}</p>
      </div>
      <Link
        href={blogUrl}
        title={`Read our comprehensive guide: ${blogTitle}`}
        aria-label={`Learn more about ${title} - ${blogTitle}`}
        className="group"
      >
        <button className="w-full flex items-center justify-end gap-2 text-primary text-sm mt-4 group-hover:text-primary/80 transition-colors duration-200">
          Learn More
          <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors duration-200">
            <ArrowRigthIcon />
          </div>
        </button>
      </Link>
    </div>
  );
}
