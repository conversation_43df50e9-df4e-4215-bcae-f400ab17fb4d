"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useBlogStore } from "@/store/blogStore";
import BannerScroll from "./components/BannerScroll";
import BlogCard, { BlogCardProps } from "./components/BlogCard";
import SearchInput from "./components/SearchInput";
import CategoryList from "./components/CategoryList";
import { Pagination } from "./components/Pagination";
import BlogCardSkeleton from "./components/BlogCardSkeleton";
import BannerScrollSkeleton from "./components/BannerScrollSkeleton";
import http from "@/services/httpService";
import Link from "next/link";

// Define the API response interface
interface BlogPostsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BlogPost[];
  categories: Category[];
}

interface Category {
  name: string;
  slug: string;
}

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  author: {
    id: number;
    email: string;
    display_name: string;
  };
  body?: string;
  snippet?: string;
  cover_image?: string;
  publish_timestamp: number;
  status: string;
  url: string;
  tags: string[];
}

interface SearchResponse {
  query: string;
  results: SearchResult[];
  count: number;
  categories: Category[];
}

interface SearchResult {
  id: number;
  title: string;
  slug: string;
  category: {
    name: string;
    slug: string;
  };
  author: string;
  publish_timestamp: number;
  snippet: string;
  body?: string;
  cover_image?: string;
  tags: string[];
  url: string;
}

const BlogPageClient = () => {
  const searchParams = useSearchParams();
  const { selectedTag, setSelectedTag, clearSelectedTag, searchQuery } =
    useBlogStore();

  const [blogPosts, setBlogPosts] = useState<BlogCardProps[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogCardProps[]>([]);
  const [loading, setLoading] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [nextPageUrl, setNextPageUrl] = useState<string | null>(null);
  const [previousPageUrl, setPreviousPageUrl] = useState<string | null>(null);
  const [dataCount, setDataCount] = useState(0);
  const [isSearchError, setIsSearchError] = useState(false);

  const pageParam = searchParams?.get("page") || "1";

  // Fetch data on component mount and when search/page/tag changes
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        const currentPageNum = Number(pageParam) || 1;
        setCurrentPage(currentPageNum);

        let data: BlogPostsResponse | SearchResponse;
        let posts: BlogCardProps[] = [];
        let cats: Category[] = [];
        let nextUrl: string | null = null;
        let prevUrl: string | null = null;
        let searchError = false;

        // If search query OR selected tag is provided, use search API
        if (searchQuery || selectedTag) {
          const queryToSearch = searchQuery || selectedTag || "";
          try {
            let searchUrl = `/api/blog/search/?q=${encodeURIComponent(
              queryToSearch
            )}`;
            if (currentPageNum > 1) {
              searchUrl += `&page=${currentPageNum}`;
            }

            const response = await http.get(searchUrl, { useAuth: false });
            data = response.data as SearchResponse;
            cats = data.categories || [];

            // Map search results and filter by tag if tag filtering is active
            let searchResults = (data.results || []).map((post) => ({
              id: post.id,
              title: post.title,
              slug: post.slug,
              author: post.author,
              snippet: post.snippet,
              publish_timestamp: post.publish_timestamp,
              url: post.url,
              tags: post.tags || [],
              category: post.category,
              cover_image: post.cover_image,
            }));

            // If we're filtering by tag (not search query), only show posts that actually have the tag
            if (selectedTag && !searchQuery) {
              const originalCount = searchResults.length;
              searchResults = searchResults.filter((post) =>
                post.tags.some(
                  (tag) => tag.toLowerCase() === selectedTag.toLowerCase()
                )
              );

              // Update the count to reflect filtered results
              data.count = searchResults.length;

              // Note: This client-side filtering may reduce the number of results per page
              // For better pagination, the backend should support exact tag matching
            }

            posts = searchResults;
          } catch (searchErr) {
            console.error("Error in search API call:", searchErr);
            searchError = true;

            // Fallback to regular posts if search fails
            let apiUrl = `/api/blog/posts/?`;
            if (currentPageNum > 1) {
              apiUrl += `page=${currentPageNum}`;
            }

            const response = await http.get(apiUrl, { useAuth: false });
            data = response.data as BlogPostsResponse;
            cats = data.categories;
            nextUrl = data.next;
            prevUrl = data.previous;

            posts = data.results.map((post) => ({
              id: post.id,
              title: post.title,
              slug: post.slug,
              author: post.author,
              snippet: post.snippet || "",
              publish_timestamp: post.publish_timestamp,
              url: post.url,
              tags: post.tags,
              cover_image: post.cover_image,
            }));
          }
        } else {
          // Fetch regular blog posts with pagination
          let apiUrl = `/api/blog/posts/?`;
          if (currentPageNum > 1) {
            apiUrl += `page=${currentPageNum}`;
          }

          const response = await http.get(apiUrl, { useAuth: false });
          data = response.data as BlogPostsResponse;
          cats = data.categories;
          nextUrl = data.next;
          prevUrl = data.previous;

          posts = data.results.map((post) => ({
            id: post.id,
            title: post.title,
            slug: post.slug,
            author: post.author,
            snippet: post.snippet || "",
            publish_timestamp: post.publish_timestamp,
            url: post.url,
            tags: post.tags,
            cover_image: post.cover_image,
          }));
        }

        // Calculate total pages
        const itemsPerPage = 10;
        const totalPagesCalc = Math.ceil(data.count / itemsPerPage);

        setBlogPosts(posts);
        setCategories(cats);
        setTotalPages(totalPagesCalc);
        setNextPageUrl(nextUrl);
        setPreviousPageUrl(prevUrl);
        setDataCount(data.count);
        setIsSearchError(searchError);
      } catch (err) {
        console.error("Error fetching blog posts:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load blog posts"
        );
      } finally {
        setLoading(false);
        setIsInitialLoad(false);
      }
    };

    fetchData();
  }, [searchQuery, pageParam, selectedTag]);

  // Update filtered posts when blog posts change (no client-side filtering needed)
  useEffect(() => {
    setFilteredPosts(blogPosts);
  }, [blogPosts]);

  const handleTagClick = (tag: string) => {
    if (selectedTag === tag) {
      clearSelectedTag();
    } else {
      setSelectedTag(tag);
    }
  };

  return (
    <div className="w-full mt-8 lg:mt-[84px] container">
      <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6">
        <div className="col-span-1 flex flex-col gap-4">
          <SearchInput />
          <CategoryList categories={categories} currentCategory={null} />
        </div>
        <div className="col-span-3 relative">
          {/* Initial Loading State - only show on first load */}
          {loading && isInitialLoad && (
            <>
              {/* Banner skeleton */}
              <BannerScrollSkeleton />

              {/* Blog cards skeleton */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6 auto-rows-fr">
                {Array.from({ length: 6 }).map((_, index) => (
                  <BlogCardSkeleton key={index} />
                ))}
              </div>
            </>
          )}

          {/* Loading State for Search/Pagination - show skeleton when loading but not initial load */}
          {loading && !isInitialLoad && !error && (
            <>
              {/* Show search results title skeleton if searching */}
              {searchQuery && (
                <div className="mb-6">
                  <div className="h-8 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 bg-[length:200%_100%] animate-[shimmer_2s_infinite] rounded w-64 mb-2"></div>
                  <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 bg-[length:200%_100%] animate-[shimmer_2s_infinite] rounded w-32"></div>
                </div>
              )}

              {/* Show tag filter title skeleton if filtering by tag */}
              {!searchQuery && selectedTag && (
                <div className="mb-6">
                  <div className="h-8 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 bg-[length:200%_100%] animate-[shimmer_2s_infinite] rounded w-48 mb-2"></div>
                  <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 bg-[length:200%_100%] animate-[shimmer_2s_infinite] rounded w-40"></div>
                </div>
              )}

              {/* Show banner skeleton only if not searching and not filtering by tag */}
              {!searchQuery && !selectedTag && <BannerScrollSkeleton />}

              {/* Blog cards skeleton */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6 auto-rows-fr">
                {Array.from({ length: 6 }).map((_, index) => (
                  <BlogCardSkeleton key={index} />
                ))}
              </div>
            </>
          )}

          {/* Error State */}
          {error && !loading && (
            <div className="text-center py-18 bg-gray-200 border border-gray-300 rounded-lg">
              <h2 className="text-xl font-bold text-gray-700 mb-2">
                Unable to Load Blog Posts
              </h2>
              <p className="text-sm text-gray-500">{error}</p>
              <Link
                href="/blog"
                className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-opacity-90 transition-all inline-block"
              >
                Try Again
              </Link>
            </div>
          )}

          {/* Content - show when not loading and no error */}
          {!loading && !error && (
            <>
              {/* Show search results title if searching */}
              {searchQuery && (
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-secondary">
                    Search results for "{searchQuery}"
                  </h2>
                  <p className="text-gray-500 mt-2">
                    Found {dataCount} {dataCount === 1 ? "result" : "results"}
                    {isSearchError && (
                      <span className="text-red-500 text-sm ml-2">
                        (Search API error - showing regular posts instead)
                      </span>
                    )}
                  </p>
                </div>
              )}

              {/* Show tag filter title if filtering by tag */}
              {!searchQuery && selectedTag && (
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-secondary">
                    Posts tagged with "{selectedTag}"
                  </h2>
                  <p className="text-gray-500 mt-2">
                    Found {dataCount} {dataCount === 1 ? "post" : "posts"}
                    <button
                      onClick={clearSelectedTag}
                      className="ml-2 text-primary hover:underline cursor-pointer"
                    >
                      (Clear filter)
                    </button>
                  </p>
                </div>
              )}

              {/* Only show banner if not searching and not filtering by tag */}
              {!searchQuery && !selectedTag && filteredPosts.length >= 3 && (
                <BannerScroll blogData={filteredPosts.slice(0, 3)} />
              )}

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6 auto-rows-fr">
                {filteredPosts.length > 0 ? (
                  filteredPosts.map((post) => (
                    <BlogCard
                      key={post.id}
                      {...post}
                      onTagClick={handleTagClick}
                      selectedTag={selectedTag}
                    />
                  ))
                ) : (
                  <div className="col-span-2 py-10 text-center">
                    <h3 className="text-xl font-semibold text-secondary">
                      {selectedTag
                        ? "No posts found with this tag"
                        : "No posts found"}
                    </h3>
                    <p className="mt-2 text-gray-500">
                      {selectedTag
                        ? "Try selecting a different tag or clear the filter"
                        : "Try a different search term"}
                    </p>
                  </div>
                )}
              </div>

              {/* Show pagination (hide for tag filtering since we do client-side filtering) */}
              {!selectedTag || searchQuery ? (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  nextPageUrl={nextPageUrl}
                  previousPageUrl={previousPageUrl}
                />
              ) : (
                <div className="text-center py-4 text-gray-500 text-sm">
                  Showing all posts with tag "{selectedTag}"
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlogPageClient;
