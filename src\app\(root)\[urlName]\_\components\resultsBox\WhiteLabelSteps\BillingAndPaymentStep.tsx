"use client";
import { useState, useCallback } from "react";
import { CheckIcon } from "@/ui/icons/general";
import StripeIcon from "@/ui/icons/payment/StripeIcon";
import paymentService from "@/services/paymentService";
import PriceDisplay from "@/components/ui/PriceDisplay";
import { extractCurrencyInfo } from "@/utils/currencyUtils";

// Define the new pricing plan interface based on the updated API response
interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: string;
  interval_count: number;
  product_id: string;
  metadata: Record<string, any>;
  price_metadata: Record<string, any>;
}

// Define types for billing period
type BillingPeriod = "monthly" | "annually";

// Define types for the plan returned by the plans() function
type WhiteLabelPlanType = {
  id: string;
  name: string;
  price: number;
  period: string;
  description: string;
  features: string[];
  apiPlanId?: number;
  product_id?: string; // Added product_id field
};

interface BillingAndPaymentStepProps {
  onNext: (planId: string, billingPeriod: BillingPeriod) => void;
  onBack: () => void;
  apiPricingData: PricingPlan[];
  initialSelectedPlan: string | null;
  initialBillingPeriod: BillingPeriod;
  taskId?: string;
  hasActiveSubscription?: boolean;
  subscriptionDetails?: {
    planName: string;
    renewalDate?: string;
  };
  skipToDownload?: () => void;
}

export default function BillingAndPaymentStep({
  onNext,
  onBack,
  apiPricingData,
  initialSelectedPlan,
  initialBillingPeriod,
  taskId,
  hasActiveSubscription = false,
  subscriptionDetails,
  skipToDownload,
}: BillingAndPaymentStepProps) {
  // State for the selected plan - default to "basic" (White Label) if not already selected
  const [selectedPlan, setSelectedPlan] = useState<string | null>(
    initialSelectedPlan || "basic"
  );

  // State for billing period (monthly or annually)
  const [billingPeriod, setBillingPeriod] =
    useState<BillingPeriod>(initialBillingPeriod);

  // State for loading
  const [isLoading, setIsLoading] = useState(false);

  // State for errors
  const [error, setError] = useState<string | null>(null);

  // Get plans directly from API data
  const plans = useCallback((): WhiteLabelPlanType[] => {
    // Default features for Pro Plan
    const defaultFeatures = [
      "All DIY features included",
      "Generate beautiful, branded PDF audit reports",
      "Add  your logo, and contact info to impress clients",
      "Local SEO audits tailored for service-area businesses",
    ];

    // If API data is available, use it
    if (apiPricingData.length > 0) {
      console.log("Using API pricing data:", apiPricingData);

      // Get the appropriate pricing data based on the billing period
      const intervalType = billingPeriod === "monthly" ? "month" : "year";

      // Find the plan based on the interval
      const plan = apiPricingData.find((p) => p.interval === intervalType);

      // If we found a plan, use it
      if (plan) {
        console.log("Found plan:", plan);

        // Create the plan object with product_id
        return [
          {
            id: "basic",
            name: plan.name || "Pro Plan",
            price: plan.price,
            period: billingPeriod === "monthly" ? "monthly" : "yearly",
            description:
              plan.description ||
              "Offer top-tier SEO reports under your own branding.",
            features: defaultFeatures,
            apiPlanId: plan.id,
            product_id: plan.product_id, // Include product_id from API data
          },
        ];
      }
    }

    console.log("Using fallback pricing data");

    // Fallback to default plans if API data is not available
    return [
      {
        id: "basic",
        name: "Pro Plan",
        price: billingPeriod === "monthly" ? 28 : 288,
        period: billingPeriod === "monthly" ? "monthly" : "yearly",
        description:
          "Offer top-tier SEO reports under your own branding. Whether you're a freelancer or growing agency, Pro Plan helps you scale client services without building a team.",
        features: defaultFeatures,
        product_id: "prod_SIYQW7A4pzIwFh", // Default product_id as fallback
      },
    ];
  }, [apiPricingData, billingPeriod]);

  // Get current plan data for currency information
  const getCurrentPlan = useCallback(() => {
    const intervalType = billingPeriod === "monthly" ? "month" : "year";
    return apiPricingData.find((p) => p.interval === intervalType);
  }, [apiPricingData, billingPeriod]);

  // Calculate dynamic save percentage for yearly billing
  const calculateSavePercentage = useCallback(() => {
    if (apiPricingData.length < 2) {
      // Fallback calculation using default prices
      const monthlyPrice = 28;
      const yearlyPrice = 288;
      const monthlyTotal = monthlyPrice * 12;
      const savings = monthlyTotal - yearlyPrice;
      return Math.round((savings / monthlyTotal) * 100);
    }

    // Find monthly and yearly plans from API data
    const monthlyPlan = apiPricingData.find((p) => p.interval === "month");
    const yearlyPlan = apiPricingData.find((p) => p.interval === "year");

    if (!monthlyPlan || !yearlyPlan) {
      return 14; // Fallback to default percentage
    }

    // Calculate the save percentage: ((monthly * 12) - yearly) / (monthly * 12) * 100
    const monthlyTotal = monthlyPlan.price * 12;
    const savings = monthlyTotal - yearlyPlan.price;
    const savePercentage = (savings / monthlyTotal) * 100;

    return Math.round(savePercentage);
  }, [apiPricingData]);

  // Handle plan selection
  const handlePlanSelect = useCallback((planId: string) => {
    setSelectedPlan(planId);
  }, []);

  // Handle billing period change
  const handleBillingPeriodChange = useCallback((period: BillingPeriod) => {
    setBillingPeriod(period);
  }, []);

  // Handle payment processing
  const handlePaymentProcess = useCallback(async () => {
    if (!selectedPlan) {
      setError("Please select a plan");
      return;
    }

    setError(null);
    setIsLoading(true);

    try {
      // Get the interval type based on billing period
      const intervalType = billingPeriod === "monthly" ? "month" : "year";

      // Find the plan in the API data
      const plan = apiPricingData.find((p) => p.interval === intervalType);

      if (!plan) {
        setError(
          "Could not find the selected plan. Please try again or contact support."
        );
        return;
      }

      console.log(
        "Creating payment session with plan ID:",
        plan.id,
        "product ID:",
        plan.product_id,
        "and task ID:",
        taskId || ""
      );

      // Create payment session with the API, including the product_id
      const response = await paymentService.createPaymentSession({
        plan_id: plan.id,
        task_id: taskId || "",
        product_id: plan.product_id, // Send the product_id to the create-session endpoint
      });

      console.log("Payment session response:", response);

      // Handle the API response format which may include redirect_url, checkout_url, or url
      if (response.redirect_url) {
        window.location.href = response.redirect_url;
        return;
      } else if (response.checkout_url) {
        window.location.href = response.checkout_url;
        return;
      } else if (response.url) {
        window.location.href = response.url;
        return;
      }

      // If no redirect URL is provided, proceed to next step
      onNext(selectedPlan, billingPeriod);
    } catch (error: any) {
      console.error("Payment processing error:", error);

      // Check if it's an authentication error
      if (error.response && error.response.status === 401) {
        setError(
          "Authentication required. Please log in to continue with your payment."
        );
      } else {
        setError("Payment processing failed. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  }, [selectedPlan, billingPeriod, apiPricingData, taskId, onNext]);

  return (
    <div className="p-3 sm:p-4 overflow-y-auto">
      <h2 className="text-lg sm:text-xl font-bold text-secondary mb-2 sm:mb-4">
        White Label Plan
      </h2>

      {hasActiveSubscription && subscriptionDetails ? (
        <div className="mb-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <div className="flex items-center mb-2">
              <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center mr-2">
                <CheckIcon className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-green-700">
                Active Subscription
              </h3>
            </div>
            <p className="text-sm text-green-700 mb-2">
              You have an active <strong>{subscriptionDetails.planName}</strong>{" "}
              subscription.
            </p>
            {subscriptionDetails.renewalDate && (
              <p className="text-sm text-green-700">
                Your subscription will renew on{" "}
                <strong>{subscriptionDetails.renewalDate}</strong>.
              </p>
            )}
          </div>
          <div className="border-t border-gray-200 pt-4 mb-4">
            <p className="text-sm text-gray-500 mb-2">
              Below is your current plan information. No payment is required at
              this time.
            </p>
          </div>
        </div>
      ) : (
        <p className="text-sm sm:text-base text-secondary mb-4 sm:mb-6">
          Review your White Label plan and proceed to payment.
        </p>
      )}

      {/* Billing period toggle - only show if no active subscription */}
      {!hasActiveSubscription && (
        <div className="flex justify-center mb-6 sm:mb-8">
          <div className="inline-flex items-center bg-gray-100 rounded-full p-1">
            <button
              className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm font-medium transition-all ${
                billingPeriod === "monthly"
                  ? "bg-white text-primary shadow-sm"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => handleBillingPeriodChange("monthly")}
            >
              Monthly
            </button>
            <button
              className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm font-medium transition-all ${
                billingPeriod === "annually"
                  ? "bg-white text-primary shadow-sm"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => handleBillingPeriodChange("annually")}
            >
              Annually
              <span className="ml-1 text-xs text-green-500 font-bold">
                Save {calculateSavePercentage()}%
              </span>
            </button>
          </div>
        </div>
      )}

      {/* Stripe payment section with White Label plan */}
      <div className="mt-4 sm:mt-6 mb-4 border border-gray-300 rounded-lg shadow-sm overflow-hidden">
        <div className="bg-gray-50 p-3 sm:p-4 border-b border-gray-200">
          <div className="flex items-center">
            <span className="text-base sm:text-xl font-semibold text-secondary mr-2 sm:mr-3">
              Stripe
            </span>
            <StripeIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
          </div>
        </div>

        <div className="p-3 sm:p-5">
          {/* Only show the White Label plan */}
          {plans()
            .filter((plan) => plan.id === "basic")
            .map((plan) => (
              <div
                key={plan.id}
                className={`border rounded-lg p-3 sm:p-5 cursor-pointer transition-all ${
                  selectedPlan === plan.id
                    ? "border-primary bg-primary/5"
                    : "border-gray-200 hover:border-primary/50"
                }`}
                onClick={() => handlePlanSelect(plan.id)}
              >
                <div className="flex items-start gap-3 sm:gap-4">
                  {/* Selection indicator */}
                  <div className="pt-1">
                    <div
                      className={`w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 flex items-center justify-center ${
                        selectedPlan === plan.id
                          ? "border-primary bg-primary text-white"
                          : "border-gray-300"
                      }`}
                    >
                      {selectedPlan === plan.id && (
                        <CheckIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                      )}
                    </div>
                  </div>

                  {/* Plan info */}
                  <div className="flex-1">
                    <h3 className="text-lg sm:text-xl font-bold text-secondary mb-1 sm:mb-2">
                      {plan.name}
                    </h3>
                    <div className="mb-2 sm:mb-3">
                      <span className="text-xl sm:text-2xl font-bold text-primary">
                        ${plan.price}
                      </span>
                      <span className="text-gray-500 ml-1 text-sm sm:text-base">
                        /{plan.period}
                      </span>
                    </div>
                    <p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4">
                      {plan.description}
                    </p>
                    <ul className="space-y-1 sm:space-y-2">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <CheckIcon className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span className="text-sm sm:text-base text-gray-700">
                            {feature}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>

      {/* Order Summary Section - only show if no active subscription */}
      {!hasActiveSubscription && (
        <div className="mt-6 mb-4 border border-gray-300 rounded-lg shadow-sm overflow-hidden">
          <div className="bg-gray-50 p-3 sm:p-4 border-b border-gray-200">
            <h3 className="text-base sm:text-lg font-semibold text-secondary">
              Order Summary
            </h3>
          </div>
          <div className="p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm sm:text-base text-gray-700">
                {billingPeriod === "monthly" ? "Monthly" : "Annual"} Plan
              </span>
              <PriceDisplay
                price={plans().find((plan) => plan.id === "basic")?.price || 0}
                size="sm"
                weight="medium"
                className="text-gray-900"
                currency={extractCurrencyInfo(getCurrentPlan()).symbol}
                currencyCode={extractCurrencyInfo(getCurrentPlan()).code}
              />
            </div>
            <div className="mb-4">
              <p className="text-xs sm:text-sm text-gray-600 mt-1">
                Offer top-tier SEO reports under your own branding. Whether
                you're a freelancer or growing agency, White Label helps you
                scale client services without building a team.
              </p>
            </div>
            <div className="border-t border-gray-200 pt-3 mb-2">
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                Plan Features:
              </h4>
              <ul className="space-y-1 mb-4">
                {plans()
                  .find((plan) => plan.id === "basic")
                  ?.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <CheckIcon className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-xs sm:text-sm text-gray-700">
                        {feature}
                      </span>
                    </li>
                  ))}
              </ul>
            </div>
            <div className="border-t border-gray-200 pt-3">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm sm:text-base text-gray-700">
                  Subtotal
                </span>
                <PriceDisplay
                  price={plans().find((plan) => plan.id === "basic")?.price || 0}
                  size="sm"
                  weight="medium"
                  className="text-gray-900"
                  currency={extractCurrencyInfo(getCurrentPlan()).symbol}
                  currencyCode={extractCurrencyInfo(getCurrentPlan()).code}
                />
              </div>
              <div className="flex justify-between items-center font-bold">
                <span className="text-sm sm:text-base text-gray-900">
                  Total
                </span>
                <PriceDisplay
                  price={plans().find((plan) => plan.id === "basic")?.price || 0}
                  size="sm"
                  weight="bold"
                  className="text-primary"
                  currency={extractCurrencyInfo(getCurrentPlan()).symbol}
                  currencyCode={extractCurrencyInfo(getCurrentPlan()).code}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-3 py-2 sm:px-4 sm:py-3 rounded-lg mb-4 text-sm sm:text-base">
          {error}
        </div>
      )}

      {/* Spacer to ensure content doesn't get hidden behind fixed button */}
      <div className="h-20 sm:h-24"></div>

      {/* Fixed Action buttons */}
      <div className="fixed bottom-0 left-0 right-0 bg-white shadow-md border-t border-gray-300 p-3 sm:p-4 z-10">
        <div className="max-w-full mx-auto flex justify-between">
          <button
            type="button"
            className="btn btn--secondary px-4 sm:px-6 py-2 text-sm sm:text-base"
            onClick={onBack}
          >
            Back
          </button>
          {hasActiveSubscription ? (
            <button
              type="button"
              className="btn btn--primary px-4 sm:px-6 py-2 text-sm sm:text-base"
              onClick={skipToDownload}
            >
              <div className="flex flex-col items-center">
                <span>Continue to Download</span>
                <span className="text-xs text-green-300 font-medium mt-1">
                  You have an active subscription for the white label service
                </span>
              </div>
            </button>
          ) : (
            <button
              type="button"
              className={`btn btn--primary px-4 sm:px-6 py-2 text-sm sm:text-base ${
                isLoading ? "opacity-70 cursor-not-allowed" : ""
              }`}
              onClick={handlePaymentProcess}
              disabled={isLoading || !selectedPlan}
            >
              {isLoading ? (
                <>
                  <span className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                  <span className="text-sm sm:text-base">Processing...</span>
                </>
              ) : (
                "Pay with Stripe"
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
