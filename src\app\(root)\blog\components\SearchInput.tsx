"use client";

import { SearchIcon } from "@/ui/icons/action";
import { useRouter, usePathname } from "next/navigation";
import { useState, useEffect, useRef } from "react";
import { useBlogStore } from "@/store/blogStore";

interface SearchInputProps {
  defaultValue?: string;
}

const SearchInput = ({ defaultValue = "" }: SearchInputProps) => {
  const { searchQuery, setSearchQuery, clearSearchQuery, clearSelectedTag } =
    useBlogStore();
  const [localQuery, setLocalQuery] = useState(searchQuery || defaultValue);
  const router = useRouter();
  const pathname = usePathname();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Sync local query with store when store changes
  useEffect(() => {
    setLocalQuery(searchQuery || defaultValue);
  }, [searchQuery, defaultValue]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalQuery(value);
  };

  // Auto-update search when local query changes
  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set a new timeout to update search after 500ms of inactivity
    timeoutRef.current = setTimeout(() => {
      // If query is empty, clear search
      if (localQuery.trim() === "") {
        clearSearchQuery();
        clearSelectedTag(); // Also clear any tag filter
        // Don't redirect when clearing search - stay on current page
      }
      // Otherwise, update search query in store
      else {
        setSearchQuery(localQuery.trim());
        clearSelectedTag(); // Clear tag filter when searching

        // Only navigate to main blog page when there's an actual search query
        if (pathname !== "/blog") {
          router.push("/blog");
        }
      }
    }, 500);

    // Cleanup on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [
    localQuery,
    setSearchQuery,
    clearSearchQuery,
    clearSelectedTag,
    router,
    pathname,
  ]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Clear any existing timeout to trigger immediate search
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // If query is empty, clear search
    if (localQuery.trim() === "") {
      clearSearchQuery();
      clearSelectedTag();
      // Don't redirect when clearing search - stay on current page
    } else {
      // Update search query in store immediately
      setSearchQuery(localQuery.trim());
      clearSelectedTag(); // Clear tag filter when searching

      // Only navigate to main blog page when there's an actual search query
      if (pathname !== "/blog") {
        router.push("/blog");
      }
    }
  };

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className="bg-[#eae3f0] py-2 px-4 rounded-lg flex gap-2 items-center">
        <input
          type="text"
          className="w-full focus-visible:outline-0 placeholder:text-light-gray-3 active:outline-0 bg-transparent"
          placeholder="Search"
          value={localQuery}
          onChange={handleInputChange}
        />
        <button
          type="submit"
          className="bg-primary text-white p-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center justify-center"
          aria-label="Search"
        >
          <SearchIcon className="text-white w-5 h-5" />
        </button>
      </div>
    </form>
  );
};

export default SearchInput;
