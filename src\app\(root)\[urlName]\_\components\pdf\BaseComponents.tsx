"use client";
import React from "react";
import { RecommendationType, ScoreGrade } from "@/types/seoAnalyzerTypes";
import { DocumentCheckIcon, DocumentCrossIcon } from "@/ui/icons/general";
import ProgressChart from "@/ui/charts/ProgressChart";

// Base Props Types
export interface SectionWatermarkProps {
  brandName?: string;
  brandWebsite?: string;
  brandPhoto?: string | null;
  onLogoLoad?: () => void;
  onLogoError?: () => void;
  showLogo?: boolean;
  logoSize?: "small" | "medium" | "large";
  sectionId?: string;
}

export interface SectionHeaderProps {
  title: string;
  brandName?: string;
  brandWebsite?: string;
}

export interface SectionScoreBoxProps {
  scoreGrade: ScoreGrade;
  description?: string;
  title?: string;
}

export interface ScoreChartProps {
  score: number;
  size?: string;
  compact?: boolean;
}

export interface ScoreDisplayProps {
  scoreGrade: ScoreGrade;
  compact?: boolean;
}

export interface RecommendationCardProps {
  recommendation: RecommendationType;
}

export interface DataRowProps {
  label: string;
  value: string | number | React.ReactNode;
  important?: boolean;
}

// Chart component for score visualization
export const ScoreChart: React.FC<ScoreChartProps> = ({
  score,
  size = "w-24 h-24",
  compact = false,
}) => {
  const circumference = 2 * Math.PI * 42; // r = 42 for better visual balance
  const strokeDashoffset = circumference - (score / 100) * circumference;

  // Use app's color scheme with primary color integration
  let color = "rgba(52, 199, 89, 1)"; // Primary green for excellent scores
  let bgColor = "rgba(52, 199, 89, 0.1)";

  if (score < 30) {
    color = "rgba(255, 59, 48, 1)"; // Primary red for poor scores
    bgColor = "rgba(255, 59, 48, 0.1)";
  } else if (score < 50) {
    color = "rgba(255, 168, 24, 1)"; // Primary orange for below average
    bgColor = "rgba(255, 168, 24, 0.1)";
  } else if (score < 70) {
    color = "rgba(255, 204, 0, 1)"; // Primary yellow for average
    bgColor = "rgba(255, 204, 0, 0.1)";
  } else if (score < 85) {
    color = "rgba(67, 176, 119, 1)"; // Secondary green for good
    bgColor = "rgba(67, 176, 119, 0.1)";
  }

  // Improved text sizing for better readability
  const scoreTextSize = compact ? "text-lg" : "text-xl";
  const labelTextSize = compact ? "text-[9px]" : "text-[10px]";
  const strokeWidth = compact ? "5" : "6";

  return (
    <div className={`relative ${size} flex items-center justify-center`}>
      {/* Subtle background circle for depth */}
      <div
        className="absolute inset-1 rounded-full opacity-20"
        style={{ backgroundColor: bgColor }}
      />
      <svg className="w-full h-full" viewBox="0 0 100 100">
        {/* Background circle with subtle styling */}
        <circle
          cx="50"
          cy="50"
          r="42"
          fill="none"
          stroke="rgba(224, 224, 224, 0.8)"
          strokeWidth={strokeWidth}
        />
        {/* Score circle with smooth animation */}
        <circle
          cx="50"
          cy="50"
          r="42"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          transform="rotate(-90 50 50)"
          className="transition-all duration-300 ease-out"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center flex-col">
        <span
          className={`${scoreTextSize} font-bold tracking-tight`}
          style={{ color }}
        >
          {Math.round(score)}
        </span>
        <span
          className={`${labelTextSize} text-gray-600 font-medium uppercase tracking-wide`}
        >
          SCORE
        </span>
      </div>
    </div>
  );
};

// Score display with grade
export const ScoreDisplay: React.FC<ScoreDisplayProps> = ({
  scoreGrade,
  compact = false,
}) => {
  const { grade, score } = scoreGrade;

  // Use app's primary color scheme for grades
  let color = "rgba(52, 199, 89, 1)"; // Primary green for excellent grades
  let bgColor = "rgba(52, 199, 89, 0.12)";
  let borderColor = "rgba(52, 199, 89, 0.3)";

  if (grade.startsWith("A")) {
    color = "rgba(52, 199, 89, 1)"; // Primary green
    bgColor = "rgba(52, 199, 89, 0.12)";
    borderColor = "rgba(52, 199, 89, 0.3)";
  } else if (grade.startsWith("B")) {
    color = "rgba(67, 176, 119, 1)"; // Secondary green
    bgColor = "rgba(67, 176, 119, 0.12)";
    borderColor = "rgba(67, 176, 119, 0.3)";
  } else if (grade.startsWith("C")) {
    color = "rgba(255, 204, 0, 1)"; // Primary yellow
    bgColor = "rgba(255, 204, 0, 0.12)";
    borderColor = "rgba(255, 204, 0, 0.3)";
  } else if (grade.startsWith("D")) {
    color = "rgba(255, 168, 24, 1)"; // Primary orange
    bgColor = "rgba(255, 168, 24, 0.12)";
    borderColor = "rgba(255, 168, 24, 0.3)";
  } else if (grade === "F") {
    color = "rgba(255, 59, 48, 1)"; // Primary red
    bgColor = "rgba(255, 59, 48, 0.12)";
    borderColor = "rgba(255, 59, 48, 0.3)";
  }

  // Improved sizing for better visual hierarchy
  const chartSize = compact ? "w-20 h-20" : "w-28 h-28";
  const gapSize = compact ? "gap-2" : "gap-3";
  const textSize = compact ? "text-sm" : "text-base";
  const paddingSize = compact ? "px-3 py-1" : "px-4 py-1.5";
  const marginTop = compact ? "mt-1" : "mt-2";

  return (
    <div className={`flex flex-col items-center ${gapSize}`}>
      <ScoreChart score={score} size={chartSize} compact={compact} />
      <div className={`text-center ${marginTop}`}>
        <div
          className={`${textSize} font-semibold ${paddingSize} rounded-lg border transition-all duration-200`}
          style={{
            color: color,
            backgroundColor: bgColor,
            borderColor: borderColor,
          }}
        >
          Grade {grade}
        </div>
      </div>
    </div>
  );
};

// Section header component - simplified to only show title and brand
export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  brandName,
  brandWebsite,
}) => {
  const displayBrand = brandName ?? brandWebsite ?? "SEO ANALYSER";

  return (
    <div className="w-full mb-6 relative pdf-section-header">
      {/* Clean header with title and brand only */}
      <div className="bg-gradient-to-r from-primary/8 to-primary/12 rounded-xl p-6 border-2 border-primary/25 print:border print:border-gray-300">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-5">
            {/* Enhanced primary accent bar */}
            <div className="w-2 h-12 bg-gradient-to-b from-primary to-primary/70 rounded-full" />
            <div>
              <h2 className="text-3xl font-bold text-primary tracking-tight leading-tight print:text-2xl">
                {title}
              </h2>
              <div className="h-1 w-20 bg-primary/50 rounded-full mt-2" />
            </div>
          </div>

          {/* Enhanced brand watermark */}
          <div className="pointer-events-none">
            <div className="text-base font-semibold text-primary/70 select-none bg-white/60 px-4 py-2 rounded-xl border-2 border-primary/25">
              {displayBrand}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Section score box component - separate component for score and description
export const SectionScoreBox: React.FC<SectionScoreBoxProps> = ({
  scoreGrade,
  description,
  title = "Section Score",
}) => {
  return (
    <div className="mb-6 p-6 bg-white border-2 border-gray-200/80 rounded-xl section-score-box">
      <div className="flex items-center gap-6">
        {/* Score Chart */}
        <div className="flex-shrink-0">
          <ProgressChart
            value={scoreGrade.grade}
            score={scoreGrade.score}
            title=""
            size="md"
            progressStates={[
              {
                label: "Score",
                value: scoreGrade.score || 0,
                isNoColor: true,
              },
            ]}
          />
        </div>

        {/* Score Details */}
        <div className="flex-grow">
          <h3 className="text-xl font-bold text-gray-800 mb-2">{title}</h3>
          <div className="flex items-center gap-4 mb-3">
            <span className="text-2xl font-bold text-primary">
              {scoreGrade.score}/100
            </span>
            <span className="px-3 py-1 bg-primary/10 text-primary font-semibold rounded-lg">
              Grade {scoreGrade.grade}
            </span>
          </div>
          {description && (
            <p className="text-gray-600 text-sm leading-relaxed">
              {description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

// Recommendation card component - styled to match main audit Recommendations.tsx
export const RecommendationCard: React.FC<RecommendationCardProps> = ({
  recommendation,
}) => {
  // Helper function to get icon based on priority (matching main audit styling)
  const getRecommendationIcon = () => {
    const priority = recommendation.priority?.toLowerCase() || "medium";

    if (priority.includes("high")) {
      return (
        <DocumentCrossIcon className="w-8 h-8 sm:w-10 sm:h-10 text-primary-red" />
      );
    } else if (priority.includes("medium")) {
      return (
        <DocumentCrossIcon className="w-8 h-8 sm:w-10 sm:h-10 text-primary-yellow" />
      );
    } else {
      return (
        <DocumentCheckIcon className="w-8 h-8 sm:w-10 sm:h-10 text-primary-green" />
      );
    }
  };

  // Helper function to get priority text
  const getPriorityText = () => {
    return recommendation.priority
      ? `${recommendation.priority
          .charAt(0)
          .toUpperCase()}${recommendation.priority.slice(1)} Priority`
      : "Medium Priority";
  };

  return (
    <div className="recommendation-item p-2.5 sm:p-3 border border-light-gray rounded-lg">
      <div className="flex gap-3 items-start">
        <div className="flex-shrink-0 mt-1">{getRecommendationIcon()}</div>

        <div className="flex-1">
          {/* Description */}
          <div className="mb-2">
            <p className="text-sm leading-relaxed text-secondary font-medium">
              {recommendation.text}
            </p>
          </div>

          {/* Priority badge */}
          <div className="flex flex-wrap items-center gap-2 mt-2">
            <div
              className={`badge recommendation-badge text-xs ${
                getPriorityText().toLowerCase().includes("high")
                  ? "badge--danger"
                  : getPriorityText().toLowerCase().includes("medium")
                  ? "badge--warning"
                  : "badge--success"
              }`}
            >
              {getPriorityText()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Data row component
export const DataRow: React.FC<DataRowProps> = ({
  label,
  value,
  important = false,
}) => (
  <div
    className={`flex justify-between items-center py-3 px-4 border-b border-gray-100/80 hover:bg-primary/5 transition-all duration-200 rounded-lg ${
      important ? "bg-primary/8 border-primary/20" : ""
    }`}
  >
    <span
      className={`${
        important ? "text-primary font-semibold" : "text-gray-700 font-medium"
      } text-sm leading-relaxed`}
    >
      {label}
    </span>
    <span
      className={`${
        important ? "text-primary font-bold" : "text-gray-800 font-semibold"
      } text-sm text-right`}
    >
      {value}
    </span>
  </div>
);
