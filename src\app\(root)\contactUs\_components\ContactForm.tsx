"use client";

import { InfoIcon } from "@/ui/icons/general";
import { FormEvent, useEffect, useState } from "react";
import http from "@/services/httpService";
import Toast from "./Toast";

declare global {
  interface Window {
    grecaptcha?: {
      ready: (callback: () => void) => void;
      execute: (
        siteKey: string,
        options: { action: string }
      ) => Promise<string>;
    };
  }
}

export default function ContactForm() {
  const [subject, setSubject] = useState("");
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"success" | "error" | null>(
    null
  );
  const [recaptchaError, setRecaptchaError] = useState<string | null>(null);
  const [recaptchaLoading, setRecaptchaLoading] = useState<boolean>(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const siteKey = process.env.NEXT_PUBLIC_CAPTCHA_SITE_KEY;
  const apiUrl = process.env.NEXT_PUBLIC_API_URL;

  useEffect(() => {
    if (!siteKey) {
      setRecaptchaError(
        "reCAPTCHA site key is missing. Please check your environment variables."
      );
      return;
    }

    setRecaptchaLoading(true);

    // Check if script already exists
    const existingScript = document.querySelector(
      'script[src*="recaptcha/api.js"]'
    );
    if (existingScript) {
      setRecaptchaLoading(false);
      return;
    }

    const script = document.createElement("script");
    script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`;
    script.async = true;
    script.onload = () => {
      setRecaptchaLoading(false);
    };
    script.onerror = () => {
      setRecaptchaError("Failed to load reCAPTCHA. Please try again later.");
      setRecaptchaLoading(false);
    };
    document.head.appendChild(script);

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, [siteKey]);

  const getRecaptchaToken = async (): Promise<string> => {
    if (!siteKey) {
      throw new Error("reCAPTCHA site key is not configured.");
    }

    if (!window.grecaptcha) {
      throw new Error("reCAPTCHA is not available. Please try again later.");
    }

    try {
      return await new Promise((resolve, reject) => {
        const grecaptcha = window.grecaptcha;
        if (!grecaptcha || typeof grecaptcha.ready !== "function") {
          reject(new Error("reCAPTCHA ready method is not available"));
          return;
        }

        grecaptcha.ready(async () => {
          try {
            if (typeof grecaptcha.execute !== "function") {
              reject(new Error("reCAPTCHA execute method is not available"));
              return;
            }

            const token = await grecaptcha.execute(siteKey, {
              action: "contact_form",
            });

            if (!token) {
              reject(new Error("Received empty token from reCAPTCHA."));
            } else {
              resolve(token);
            }
          } catch (error) {
            reject(
              error instanceof Error
                ? error
                : new Error(`reCAPTCHA execution failed: ${error}`)
            );
          }
        });
      });
    } catch (error) {
      throw error;
    }
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    // Subject validation
    if (!subject.trim()) {
      newErrors.subject = "Subject is required.";
    } else if (subject.trim().length < 3) {
      newErrors.subject = "Subject must be at least 3 characters long.";
    } else if (subject.trim().length > 100) {
      newErrors.subject = "Subject must be less than 100 characters.";
    }

    // Email validation
    if (!email.trim()) {
      newErrors.email = "Email address is required.";
    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email)) {
      newErrors.email = "Please enter a valid email address.";
    }

    // Message validation
    if (!message.trim()) {
      newErrors.message = "Message cannot be empty.";
    } else if (message.trim().length < 10) {
      newErrors.message = "Message must be at least 10 characters long.";
    } else if (message.trim().length > 1000) {
      newErrors.message = "Message must be less than 1000 characters.";
    }

    return newErrors;
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitStatus(null);
    setIsSubmitting(true);

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      setIsSubmitting(false);
      return;
    }

    let recaptchaToken = "";

    try {
      recaptchaToken = await getRecaptchaToken();
    } catch (error) {
      setSubmitStatus("error");
      setToastMessage("reCAPTCHA verification failed. Please try again later.");
      setShowToast(true);
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await http.post("/api/contact/", {
        subject: subject.trim(),
        email: email.trim(),
        text: message.trim(),
        recaptcha_token: recaptchaToken,
      });

      if (response.status !== 200 && response.status !== 201) {
        throw new Error("Failed to submit form");
      }

      // Show success toast
      setToastMessage("Your message has been sent successfully!");
      setShowToast(true);

      // Reset form
      setSubmitStatus("success");
      setSubject("");
      setEmail("");
      setMessage("");
      setErrors({});
    } catch (error) {
      setSubmitStatus("error");
      setToastMessage("Failed to send message. Please try again later.");
      setShowToast(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {showToast && (
        <Toast
          message={toastMessage}
          type={submitStatus === "success" ? "success" : "error"}
          onClose={() => setShowToast(false)}
        />
      )}
      <form
        onSubmit={handleSubmit}
        id="contact_form"
        className="space-y-6 contact_form"
      >
        <div className="flex flex-col gap-2">
          <label
            htmlFor="subject"
            className="text-secondary text-sm lg:text-base"
          >
            Subject
          </label>
          <input
            name="subject"
            placeholder="Type here."
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            className={`textField__input ${
              errors.subject ? "border-red-500" : ""
            }`}
          />
          {errors.subject && (
            <p className="text-red-500 text-xs mt-1">{errors.subject}</p>
          )}
        </div>

        <div className="flex flex-col gap-2">
          <label
            htmlFor="email"
            className="text-secondary text-sm lg:text-base"
          >
            E-mail Address
          </label>
          <input
            name="email"
            placeholder="Type your email here."
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className={`textField__input ${
              errors.email ? "border-red-500" : ""
            }`}
          />
          {errors.email && (
            <p className="text-red-500 text-xs mt-1">{errors.email}</p>
          )}
        </div>

        <div className="flex flex-col gap-2">
          <label htmlFor="message" className="text-secondary">
            Message
          </label>
          <textarea
            name="message"
            placeholder="Type here."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className={`textField__input ${
              errors.message ? "border-red-500" : ""
            }`}
            rows={2}
          ></textarea>
          {errors.message && (
            <p className="text-red-500 text-xs mt-1">{errors.message}</p>
          )}
        </div>
        <p className="w-full text-xs text-[#17191DB2] text-center lg:text-left">
          By filling out this form you agree to the terms and conditions in our
          privacy notice.
        </p>

        {recaptchaLoading && (
          <div className="p-4 bg-blue-100 text-blue-700 rounded-lg">
            Loading reCAPTCHA... Please wait.
          </div>
        )}

        {/* {!siteKey && (
        <div className="p-4 bg-yellow-100 text-yellow-700 rounded-lg">
          reCAPTCHA site key is missing. Please check your environment variables.
        </div>
      )} */}

        <div className="w-full flex justify-center items-center">
          <div
            className="g-recaptcha-badge"
            style={{ visibility: "hidden" }}
          ></div>
          {/* <p className="text-xs text-gray-500">This site is protected by reCAPTCHA</p> */}
        </div>

        <button
          className="text-white py-3 font-semibold rounded-md border-primary bg-primary/90 w-full hover:bg-primary/80 transition-all !font-normal disabled:opacity-50"
          type="submit"
          disabled={
            isSubmitting ||
            !!recaptchaError ||
            !siteKey ||
            !apiUrl ||
            recaptchaLoading
          }
        >
          {isSubmitting ? "Submitting..." : "Submit"}
        </button>
      </form>
    </>
  );
}
