"use client";

type Props = {
  value: string;
  label: string;
  isActive: boolean;
  onClick: (value: string) => void;
  className?: string;
};

export default function TabButton({
  isActive,
  label,
  onClick,
  value,
  className = "",
}: Props) {
  return (
    <button
      onClick={() => onClick(value)}
      className={`${
        isActive
          ? "bg-white text-primary font-semibold border-t border-l border-r border-gray-200"
          : "bg-gray-50 text-secondary/70 hover:bg-gray-100 border-b border-gray-200"
      } px-1.5 sm:px-3 md:px-4 lg:px-6 py-1.5 sm:py-2 lg:py-2.5 rounded-t-lg text-xs sm:text-sm transition-all duration-200 ${className}`}
    >
      {label}
    </button>
  );
}
