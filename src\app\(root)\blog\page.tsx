import { Metadata } from "next";
import BlogPageClient from "./BlogPageClient";

// Generate metadata for the blog page
export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "SEO Analyser Blog | Expert SEO Tips, Guides & Industry Insights",
    description:
      "Discover expert SEO tips, comprehensive guides, and latest industry insights. Learn keyword research, on-page optimization, technical SEO, and digital marketing strategies to boost your website's rankings.",
    keywords: [
      "SEO Analyser",
      "SEO blog",
      "SEO tips",
      "SEO guides",
      "keyword research",
      "on-page SEO",
      "technical SEO",
      "backlink building",
      "local SEO",
      "SEO audit",
      "search engine optimization",
      "website optimization",
      "SEO strategies",
      "SEO best practices",
    ],
    openGraph: {
      title: "SEO Blog | Expert SEO Tips, Guides & Industry Insights",
      description:
        "Discover expert SEO tips, comprehensive guides, and latest industry insights. Learn keyword research, on-page optimization, technical SEO, and digital marketing strategies to boost your website's rankings.",
      type: "website",
      url: "https://seoanalyser.com.au/blog",
      siteName: "SEO Analyser",
      images: [
        {
          url: "https://seoanalyser.com.au/images/appLogo.svg",
          width: 1200,
          height: 630,
          alt: "SEO Analyser Blog - Expert SEO Tips and Guides",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      site: "@SEO_ANALYSER",
      creator: "@SEO_ANALYSER",
      title: "SEO Blog | Expert SEO Tips, Guides & Industry Insights",
      description:
        "Discover expert SEO tips, comprehensive guides, and latest industry insights. Learn keyword research, on-page optimization, technical SEO, and digital marketing strategies to boost your website's rankings.",
      images: ["https://seoanalyser.com.au/images/appLogo.svg"],
    },
    alternates: {
      canonical: "https://seoanalyser.com.au/blog",
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

// Server component that renders the client component
const BlogPage = () => {
  return <BlogPageClient />;
};

export default BlogPage;
