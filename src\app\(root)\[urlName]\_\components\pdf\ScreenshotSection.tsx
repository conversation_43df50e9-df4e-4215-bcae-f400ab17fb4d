"use client";
import React, { useState } from "react";
import Image from "next/image";
import { SectionHeader } from "./BaseComponents";
import { SectionWatermark, PlaceholderIcon } from "./WatermarkComponents";

export interface ScreenshotSectionProps {
  screenshotUrl: string | null;
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

export const ScreenshotSection: React.FC<ScreenshotSectionProps> = ({
  screenshotUrl,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  const [failedImages, setFailedImages] = useState<Record<string, boolean>>({});
  const [imagesLoaded, setImagesLoaded] = useState<Record<string, boolean>>({});

  const handleImageError = (id: string) => {
    setFailedImages((prev) => ({
      ...prev,
      [id]: true,
    }));
    onImageError?.(id);
  };

  const handleImageLoad = (id: string) => {
    setImagesLoaded((prev) => ({
      ...prev,
      [id]: true,
    }));
    onImageLoad?.(id);
  };

  return (
    <div className="relative">
      {/* Section watermark */}
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => handleImageLoad("sectionLogo")}
        onLogoError={() => handleImageError("sectionLogo")}
        sectionId="screenshot"
      />

      {/* Section header */}
      <SectionHeader
        title="Website Screenshot"
        brandName={brand_name}
        brandWebsite={brand_website}
      />

      {/* Professional screenshot container with reduced height for better page utilization */}
      <div className="w-full bg-white rounded-xl border border-gray-200/80 overflow-hidden">
        <div className="p-4">
          <div className="w-full h-64 relative bg-gradient-to-br from-gray-50 to-gray-100/50 rounded-lg border border-gray-200/60 flex justify-center items-center overflow-hidden">
            {screenshotUrl && !failedImages["screenshot"] ? (
              <div className="relative w-full h-full flex justify-center items-center">
                <Image
                  src={screenshotUrl}
                  alt="Website Screenshot"
                  width={600}
                  height={400}
                  className="object-contain max-h-64 rounded-lg"
                  unoptimized
                  priority
                  loading="eager"
                  crossOrigin="anonymous"
                  onLoad={() => handleImageLoad("screenshot")}
                  onError={() => {
                    console.error("Screenshot failed to load");
                    handleImageError("screenshot");
                  }}
                />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center text-center">
                <div className="w-16 h-16 mb-4 text-gray-400">
                  <PlaceholderIcon />
                </div>
                <p className="text-gray-500 text-sm font-medium">
                  {failedImages["screenshot"]
                    ? "Screenshot failed to load"
                    : "Screenshot not available"}
                </p>
                {/* Mark as failed if screenshot URL exists but failed to load */}
                {screenshotUrl &&
                  !failedImages["screenshot"] &&
                  !imagesLoaded["screenshot"] && (
                    <script
                      dangerouslySetInnerHTML={{
                        __html: `setTimeout(() => {
                        if (!window.__screenshotLoaded) {
                          document.dispatchEvent(new CustomEvent('screenshot-failed'));
                        }
                      }, 1000);`,
                      }}
                    />
                  )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
