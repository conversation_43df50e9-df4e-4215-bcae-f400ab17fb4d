"use client";

import React from "react";

interface CatItemProps {
  title: string;
  isActive: boolean;
  onClick: () => void;
}
const CatItem: React.FC<CatItemProps> = ({ title, isActive, onClick }) => {
  return (
    <div
      onClick={onClick}
      className={`rounded-lg px-4 py-2 transition-all text-secondary cursor-pointer ${
        isActive ? "bg-[#eae3f0] text-primary" : ""
      }`}
    >
      {title}
    </div>
  );
};

export default CatItem;
