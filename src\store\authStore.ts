import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import authService from "@/services/authService";
import profileService, { UserProfile } from "@/services/profileService";
import storageService from "@/services/storageService";
import authErrorHandler from "@/utils/authErrorHandler";
import errorHand<PERSON>, { AppError, ErrorType } from "@/utils/errorHandler";

// Enhanced error type for better error handling
interface AuthError {
  code: string;
  message: string;
  field?: string;
  details?: any;
}

interface LoginData {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  confirm_password: string;
  first_name: string;
  last_name: string;
}

interface VerifyEmailData {
  email: string;
  otp: string;
}

interface AuthState {
  // User state
  user: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  formattedError: AppError | null;
  errors: AuthError[]; // Array of structured errors

  // Auth check state
  lastAuthCheck: number | null; // Timestamp of last successful auth check
  isCheckingAuth: boolean; // Flag to prevent concurrent auth checks
  authCheckPromise: Promise<boolean> | null; // Store the current auth check promise

  // Modal state
  isAuthModalOpen: boolean;
  isAuthModalLoading: boolean;
  shouldShowAuthModal: boolean;
  authModalView: "login-register" | "login" | "register" | "otp" | "success";
  authModalEmail: string;
  authModalActionType: "pdf" | "share" | "whiteLabel" | "general";
  authModalCallback: (() => void) | null;
  authModalShowSteps: boolean;

  // Error handling
  handleError: (error: any) => void;
  clearErrors: () => void;

  // No session timer management - rely on token expiry

  // Authentication actions
  login: (data: LoginData) => Promise<boolean>;
  register: (
    data: RegisterData
  ) => Promise<{ success: boolean; email?: string; fieldErrors?: Record<string, string[]> | null }>;
  verifyEmail: (data: VerifyEmailData) => Promise<boolean>;
  resendOtp: (email: string) => Promise<boolean>;
  logout: () => void;
  checkAuth: (force?: boolean) => Promise<boolean>; // New centralized auth check
  fetchProfile: (force?: boolean) => Promise<boolean>; // Kept for backward compatibility

  // Modal actions
  openAuthModal: (
    view?: "login-register" | "login" | "register",
    actionType?: "pdf" | "share" | "whiteLabel" | "general",
    callback?: (() => void) | null,
    showSteps?: boolean
  ) => void;
  closeAuthModal: () => void;
  setAuthModalView: (
    view: "login-register" | "login" | "register" | "otp" | "success"
  ) => void;
  setAuthModalEmail: (email: string) => void;
  handleAuthSuccess: () => void;
  checkAuthAndShowModal: () => Promise<void>;

  // Utility
  requireLogin: (callback: () => void) => boolean;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        formattedError: null,
        errors: [], // Initialize empty errors array

        // Auth check state
        lastAuthCheck: null,
        isCheckingAuth: false,
        authCheckPromise: null,

        // Modal state
        isAuthModalOpen: false,
        isAuthModalLoading: false,
        shouldShowAuthModal: false,
        authModalView: "login-register",
        authModalEmail: "",
        authModalActionType: "pdf",
        authModalCallback: null,
        authModalShowSteps: false,

        // Error handling
        handleError: (error: any) => {
          console.error("Auth error:", error);

          // Process the error into a structured format
          const processedErrors = errorHandler.processApiError(error);
          const errorMessage = processedErrors.message || "An error occurred";

          // Update state with error information
          set({
            error: errorMessage,
            formattedError: errorHandler.formatAuthError(error),
            errors: Array.isArray(processedErrors)
              ? processedErrors
              : [processedErrors],
          });

          // Check if it's an auth error (401)
          if (error.response?.status === 401) {
            authErrorHandler.notifyAuthError();
          }
        },

        clearErrors: () => {
          set({ error: null, formattedError: null, errors: [] });
        },

        // No session timer management - rely on token expiry and 401 errors

        // Actions
        login: async (data: LoginData) => {
          set({ isLoading: true });
          get().clearErrors();

          try {
            const response = await authService.login(data);

            if (
              response.statusCode &&
              response.statusCode >= 200 &&
              response.statusCode < 300
            ) {
              console.log("Login successful, fetching user profile...");

              // Force fetch user profile after successful login to ensure we have the latest data
              const profileResult = await get().checkAuth(true);

              if (profileResult) {
                console.log("Profile fetched successfully after login");
                set({
                  isAuthenticated: true,
                });
                return true;
              } else {
                console.error("Failed to fetch profile after successful login");
                // Even if profile fetch fails, we still consider login successful
                // since the token was obtained successfully
                set({
                  isAuthenticated: true,
                });
                return true;
              }
            } else {
              // Handle error
              get().handleError(response);
              return false;
            }
          } catch (error) {
            // Handle error
            get().handleError(error);
            return false;
          } finally {
            set({ isLoading: false });
          }
        },

        register: async (data: RegisterData) => {
          set({ isLoading: true });
          get().clearErrors();

          try {
            const response = await authService.register(data);

            if (
              response.statusCode &&
              response.statusCode >= 200 &&
              response.statusCode < 300
            ) {
              return { success: true, email: data.email };
            } else {
              // Handle error
              get().handleError(response);
              // Return field errors if they exist
              return {
                success: false,
                fieldErrors: response.fieldErrors || null
              };
            }
          } catch (error) {
            // Handle error
            get().handleError(error);
            return { success: false };
          } finally {
            set({ isLoading: false });
          }
        },

        verifyEmail: async (data: VerifyEmailData) => {
          set({ isLoading: true });
          get().clearErrors();

          try {
            const response = await authService.verifyEmail(data);

            if (
              response.statusCode &&
              response.statusCode >= 200 &&
              response.statusCode < 300
            ) {
              console.log(
                "Email verification successful, fetching user profile..."
              );

              // Force fetch user profile after successful verification
              const profileResult = await get().checkAuth(true);

              if (profileResult) {
                console.log(
                  "Profile fetched successfully after email verification"
                );
                set({
                  isAuthenticated: true,
                });
                return true;
              } else {
                console.error(
                  "Failed to fetch profile after successful email verification"
                );
                // Even if profile fetch fails, we still consider verification successful
                set({
                  isAuthenticated: true,
                });
                return true;
              }
            } else {
              // Handle error
              get().handleError(response);
              return false;
            }
          } catch (error) {
            // Handle error
            get().handleError(error);
            return false;
          } finally {
            set({ isLoading: false });
          }
        },

        logout: () => {
          authService.logout();

          set({
            user: null,
            isAuthenticated: false,
          });

          get().clearErrors();
        },

        // Centralized auth check function (renamed from fetchProfile for clarity)
        checkAuth: async (force = false) => {
          const state = get();

          // Check if we're already checking auth to prevent duplicate calls
          if (state.isCheckingAuth && state.authCheckPromise) {
            console.log(
              "Auth check already in progress, returning existing promise"
            );
            return state.authCheckPromise;
          }

          // Check if we have a recent auth check (within last 30 seconds) and not forcing refresh
          const now = Date.now();
          const AUTH_CHECK_THRESHOLD = 30000; // 30 seconds

          if (
            !force &&
            state.lastAuthCheck &&
            now - state.lastAuthCheck < AUTH_CHECK_THRESHOLD
          ) {
            console.log(
              "Using cached auth status, last check was",
              Math.round((now - state.lastAuthCheck) / 1000),
              "seconds ago"
            );
            return state.isAuthenticated;
          }

          // Set loading state and mark that we're checking auth
          set({
            isLoading: true,
            isCheckingAuth: true,
          });

          // Create a promise for this auth check
          const authCheckPromise = (async () => {
            try {
              console.log("Performing auth check");
              const isAuth = await authService.isAuthenticated();

              if (!isAuth) {
                console.log("Auth check failed: Token is invalid or expired");
                set({
                  isAuthenticated: false,
                  user: null,
                  isLoading: false,
                  isCheckingAuth: false,
                  lastAuthCheck: now,
                });
                return false;
              }

              console.log("Auth token is valid, fetching user profile data...");
              const response = await profileService.getUserProfile();

              if (response.success && response.data) {
                console.log("Profile data fetched successfully:", {
                  email: response.data.email,
                  name: `${response.data.first_name} ${response.data.last_name}`,
                  isVerified: response.data.is_verified,
                  subscriptions: response.data.subscriptions?.length || 0,
                });

                set({
                  user: response.data,
                  isAuthenticated: true,
                  isLoading: false,
                  isCheckingAuth: false,
                  lastAuthCheck: now,
                });
                return true;
              } else {
                console.error(
                  "Failed to fetch profile data:",
                  response.error || "Unknown error"
                );
                set({
                  isAuthenticated: false,
                  user: null,
                  isLoading: false,
                  isCheckingAuth: false,
                  lastAuthCheck: now,
                });
                return false;
              }
            } catch (error) {
              console.error("Error during auth check:", error);
              get().handleError(error);
              set({
                isAuthenticated: false,
                user: null,
                isLoading: false,
                isCheckingAuth: false,
                lastAuthCheck: now,
              });
              return false;
            }
          })();

          // Store the promise in state
          set({ authCheckPromise });

          // Return the promise
          return authCheckPromise;
        },

        // Keep fetchProfile for backward compatibility
        fetchProfile: async (force = false) => {
          return get().checkAuth(force);
        },

        // Resend OTP verification code
        resendOtp: async (email: string) => {
          set({ isLoading: true });
          get().clearErrors();

          try {
            const response = await authService.resendOtp({ email });

            if (
              response.statusCode &&
              response.statusCode >= 200 &&
              response.statusCode < 300
            ) {
              return true;
            } else {
              // Handle error
              get().handleError(response);
              return false;
            }
          } catch (error) {
            // Handle error
            get().handleError(error);
            return false;
          } finally {
            set({ isLoading: false });
          }
        },

        // Modal actions
        openAuthModal: (
          view = "login-register",
          actionType = "general",
          callback = null,
          showSteps = false
        ) => {
          set({
            isAuthModalOpen: true,
            authModalView: view,
            authModalActionType: actionType,
            authModalCallback: callback,
            authModalShowSteps: showSteps,
            isAuthModalLoading: true,
            shouldShowAuthModal: false,
          });

          get().clearErrors();

          // Trigger the auth check when modal is opened
          get().checkAuthAndShowModal();
        },

        closeAuthModal: () => {
          set({
            isAuthModalOpen: false,
            authModalCallback: null,
            shouldShowAuthModal: false,
            isAuthModalLoading: false,
          });

          get().clearErrors();
        },

        setAuthModalView: (view) => {
          set({ authModalView: view });
          get().clearErrors();
        },

        setAuthModalEmail: (email) => {
          set({ authModalEmail: email });
        },

        checkAuthAndShowModal: async () => {
          const { isAuthenticated, checkAuth, handleAuthSuccess, user } = get();

          try {
            console.log("Checking auth status for modal", {
              isAuthenticated,
              hasUserData: !!user,
            });

            // Check if user is already authenticated based on cached state
            if (isAuthenticated && user) {
              console.log(
                "User is already authenticated with profile data, skipping auth modal"
              );
              handleAuthSuccess();
              set({
                isAuthModalLoading: false,
                shouldShowAuthModal: false,
              });
              return;
            }

            // If authenticated but no user data, force fetch profile
            if (isAuthenticated && !user) {
              console.log(
                "User is authenticated but missing profile data, fetching profile"
              );
              const profileResult = await checkAuth(true);

              if (profileResult) {
                console.log(
                  "Profile fetched successfully for authenticated user"
                );
                handleAuthSuccess();
                set({
                  isAuthModalLoading: false,
                  shouldShowAuthModal: false,
                });
                return;
              }
            }

            // If not authenticated in cached state, try to fetch profile to verify token
            console.log(
              "User not authenticated in cached state, checking with server"
            );
            const isAuth = await checkAuth(true); // Force refresh to ensure we have latest data

            if (isAuth) {
              // User is authenticated after profile check
              console.log("User authenticated after profile check");
              handleAuthSuccess();
              set({
                isAuthModalLoading: false,
                shouldShowAuthModal: false,
              });
              return;
            }

            // User is not authenticated, show login/register options
            console.log(
              "User is not authenticated, showing login/register options"
            );
            set({
              authModalView: "login-register",
              isAuthModalLoading: false,
              shouldShowAuthModal: true,
            });
          } catch (error) {
            console.error("Error checking authentication status:", error);
            // If there's an error, default to not authenticated
            get().handleError(error);
            set({
              authModalView: "login-register",
              isAuthModalLoading: false,
              shouldShowAuthModal: true,
            });
          }
        },

        handleAuthSuccess: () => {
          const { authModalCallback, user } = get();

          console.log("Auth success handler called", {
            hasCallback: !!authModalCallback,
            hasUserData: !!user,
          });

          // Close the modal
          set({
            isAuthModalOpen: false,
            shouldShowAuthModal: false,
            isAuthModalLoading: false,
          });

          // Execute the callback if provided
          if (authModalCallback) {
            console.log("Executing auth modal callback");

            // Small delay to ensure state is updated before callback executes
            setTimeout(() => {
              if (authModalCallback) authModalCallback();
              set({ authModalCallback: null });
            }, 100);
          }
        },

        // Utility
        requireLogin: (callback) => {
          const { isAuthenticated, openAuthModal } = get();

          if (isAuthenticated) {
            callback();
            return true;
          } else {
            openAuthModal("login-register", "general", callback, false);
            return false;
          }
        },
      }),
      {
        name: "auth-storage",
        partialize: (state) => ({
          isAuthenticated: state.isAuthenticated,
          user: state.user,
          lastAuthCheck: state.lastAuthCheck,
        }),
      }
    )
  )
);

// Initialize auth error handler
// This will open the auth modal when a 401 error is detected
const initAuthErrorHandler = (
  openAuthModalFn: (
    view?: "login-register" | "login" | "register",
    actionType?: "pdf" | "share" | "whiteLabel" | "general",
    callback?: (() => void) | null,
    showSteps?: boolean
  ) => void
) => {
  // Define the handler function
  const handleAuthError = () => {
    console.log("Auth error handler called, opening auth modal");
    openAuthModalFn("login-register", "general", null, false);
  };

  // Register the handler with the auth error handler
  authErrorHandler.addAuthErrorListener(handleAuthError);

  // Return a cleanup function
  return () => {
    authErrorHandler.removeAuthErrorListener(handleAuthError);
  };
};

// Complete the implementation in authErrorHandler.ts
authErrorHandler.handleAuthError = function () {
  this.notifyAuthError();
};

// Initialize auth error handler with the openAuthModal function after store is created
setTimeout(() => {
  try {
    const openModalFn = useAuthStore.getState().openAuthModal;
    if (openModalFn) {
      initAuthErrorHandler(openModalFn);
      console.log("Auth error handler initialized successfully");
    }
  } catch (error) {
    console.error("Failed to initialize auth error handler:", error);
  }
}, 1000);
