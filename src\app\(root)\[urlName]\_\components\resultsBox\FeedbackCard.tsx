import Link from "next/link";
import React from "react";

type Props = {
  title?: string;
  description: string;
  link?: string;
};

export default function FeedbackCard({ description, title, link }: Props) {
  return (
    <div className="flex-1 p-6 rounded-lg bg-primary/10">
      <div className="font-semibold text-primary">{title}</div>
      <p className="text-sm text-primary/60 mt-2">{description}</p>
      {link && (
        <Link
          href={link}
          className="text-primary text-sm mt-4 inline-block"
        >
          Learn more
        </Link>
      )}
    </div>
  );
}
