import ProgressChart from "@/ui/charts/ProgressChart";
import UsabilityTable from "./UsabilityTable";
import { DocumentCheckIcon, DocumentCrossIcon } from "@/ui/icons/general";
import ShowMoreSection from "./ShowMoreSection";

// Type for recommendation
type RecommendationType = {
  text: string;
  priority: string;
};

// Type for Core Web Vitals data
type CoreWebVitalsData = {
  core_web_vitals_desktop?: {
    pass: boolean;
    "Largest Contentful Paint (LCP)"?: number;
    "Interaction to Next Paint (INP)"?: number | null;
    "Cumulative Layout Shift (CLS)"?: number;
    description: string;
    importance: string;
    recommendation: string | RecommendationType;
  };
};

// Type for Page Speed Desktop data
type PageSpeedDesktop = {
  performance_desktop?: {
    pass: boolean;
    performance_score: number;
    "First Contentful Paint (FCP)"?: number;
    "Speed Index (SI)"?: number;
    "Largest Contentful Paint (LCP)"?: number;
    "Time to Interactive (TTI)"?: number;
    "Total Blocking Time (TBT)"?: number;
    "Cumulative Layout Shift (CLS)"?: number;
    "Interaction To Next Paint (INP)"?: number | null;
    top_opportunities_ms_savings?: {
      [key: string]: number;
    };
    recommendation: string | RecommendationType;
    description: string;
    importance: string;
    blog?: string;
  };
} & CoreWebVitalsData;

// Type for Core Web Vitals Mobile data
type CoreWebVitalsMobileData = {
  core_web_vitals_mobile?: {
    pass: boolean;
    "Largest Contentful Paint (LCP)"?: number;
    "Interaction to Next Paint (INP)"?: number | null;
    "Cumulative Layout Shift (CLS)"?: number;
    description: string;
    importance: string;
    recommendation: string | RecommendationType;
  };
};

// Type for Page Speed Mobile data
type PageSpeedMobile = {
  performance_mobile?: {
    pass: boolean;
    performance_score: number;
    "First Contentful Paint (FCP)"?: number;
    "Speed Index (SI)"?: number;
    "Largest Contentful Paint (LCP)"?: number;
    "Time to Interactive (TTI)"?: number;
    "Total Blocking Time (TBT)"?: number;
    "Cumulative Layout Shift (CLS)"?: number;
    "Interaction To Next Paint (INP)"?: number | null;
    top_opportunities_ms_savings?: {
      [key: string]: number;
    };
    recommendation: string | RecommendationType;
    description: string;
    importance: string;
    blog?: string;
  };
} & CoreWebVitalsMobileData;

export default function GooglePageSpeed({
  pageSpeedDesktop,
  pageSpeedMobile,
}: {
  pageSpeedDesktop?: PageSpeedDesktop | null;
  pageSpeedMobile?: PageSpeedMobile | null;
}) {
  // Process Core Web Vitals Desktop data
  const coreWebVitalsDesktop =
    pageSpeedDesktop && pageSpeedDesktop.core_web_vitals_desktop
      ? Object.entries(pageSpeedDesktop.core_web_vitals_desktop)
          .filter(
            ([key]) =>
              !["pass", "description", "importance", "recommendation"].includes(
                key
              )
          )
          .map(([key, value]) => ({
            labData: key,
            value: value !== null ? value.toString() : "N/A",
          }))
      : [];

  // Process Desktop metrics
  const desktopMetrics =
    pageSpeedDesktop && pageSpeedDesktop.performance_desktop
      ? Object.entries(pageSpeedDesktop.performance_desktop)
          .filter(
            ([key]) =>
              ![
                "pass",
                "performance_score",
                "top_opportunities_ms_savings",
                "recommendation",
                "description",
                "importance",
                "blog",
              ].includes(key)
          )
          .map(([key, value]) => ({
            labData: key,
            value: value !== null ? value.toString() : "N/A",
          }))
      : [];

  // Process Desktop opportunities
  const desktopOpportunities =
    pageSpeedDesktop &&
    pageSpeedDesktop.performance_desktop &&
    pageSpeedDesktop.performance_desktop.top_opportunities_ms_savings
      ? Object.entries(
          pageSpeedDesktop.performance_desktop.top_opportunities_ms_savings
        ).map(([key, value]) => ({
          labData: key,
          value: value !== null ? value.toString() + "s" : "N/A",
        }))
      : [];

  // Process Core Web Vitals Mobile data
  const coreWebVitalsMobile =
    pageSpeedMobile && pageSpeedMobile.core_web_vitals_mobile
      ? Object.entries(pageSpeedMobile.core_web_vitals_mobile)
          .filter(
            ([key]) =>
              !["pass", "description", "importance", "recommendation"].includes(
                key
              )
          )
          .map(([key, value]) => ({
            labData: key,
            value: value !== null ? value.toString() : "N/A",
          }))
      : [];

  // Process Mobile metrics
  const mobileMetrics =
    pageSpeedMobile && pageSpeedMobile.performance_mobile
      ? Object.entries(pageSpeedMobile.performance_mobile)
          .filter(
            ([key]) =>
              ![
                "pass",
                "performance_score",
                "top_opportunities_ms_savings",
                "recommendation",
                "description",
                "importance",
                "blog",
              ].includes(key)
          )
          .map(([key, value]) => ({
            labData: key,
            value: value !== null ? value.toString() : "N/A",
          }))
      : [];

  // Process Mobile opportunities
  const mobileOpportunities =
    pageSpeedMobile &&
    pageSpeedMobile.performance_mobile &&
    pageSpeedMobile.performance_mobile.top_opportunities_ms_savings
      ? Object.entries(
          pageSpeedMobile.performance_mobile.top_opportunities_ms_savings
        ).map(([key, value]) => ({
          labData: key,
          value: value !== null ? value.toString() : "N/A",
        }))
      : [];

  // Get desktop score
  const desktopScore =
    pageSpeedDesktop &&
    pageSpeedDesktop.performance_desktop &&
    pageSpeedDesktop.performance_desktop.performance_score
      ? pageSpeedDesktop.performance_desktop.performance_score
      : 0;

  // Get mobile score
  const mobileScore =
    pageSpeedMobile &&
    pageSpeedMobile.performance_mobile &&
    pageSpeedMobile.performance_mobile.performance_score
      ? pageSpeedMobile.performance_mobile.performance_score
      : 0;

  return (
    <div className="mt-6">
      {/* Core Web Vitals Desktop Section */}
      {pageSpeedDesktop && pageSpeedDesktop.core_web_vitals_desktop && (
        <div className="mb-6">
          <ShowMoreSection
            title="Google's Core Web Vitals - Desktop"
            description={pageSpeedDesktop.core_web_vitals_desktop.description}
            passed={pageSpeedDesktop.core_web_vitals_desktop.pass}
            importance={pageSpeedDesktop.core_web_vitals_desktop.importance}
            recommendation={
              pageSpeedDesktop.core_web_vitals_desktop.recommendation
            }
            icon={
              pageSpeedDesktop.core_web_vitals_desktop.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
          >
            <div className="flex flex-col lg:flex-row items-center lg:items-start gap-4 lg:gap-6 pagespeed-table">
              <UsabilityTable data={coreWebVitalsDesktop} />
            </div>
          </ShowMoreSection>
        </div>
      )}

      {/* Desktop PageSpeed Section */}
      {pageSpeedDesktop && pageSpeedDesktop.performance_desktop && (
        <div className="mb-6">
          <ShowMoreSection
            title="Page Speed - Desktop"
            description={pageSpeedDesktop.performance_desktop.description}
            passed={pageSpeedDesktop.performance_desktop.pass}
            importance={pageSpeedDesktop.performance_desktop.importance}
            recommendation={pageSpeedDesktop.performance_desktop.recommendation}
            icon={
              pageSpeedDesktop.performance_desktop.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
          >
            <div className="mt-2 lg:mt-4 flex justify-center ">
              <ProgressChart
                score={desktopScore}
                title="Desktop PageSpeed Score"
                size="md"
                showGrade={false}
                progressStates={[
                  {
                    label: "Score",
                    value: desktopScore,
                    isNoColor: false,
                  },
                ]}
              />
            </div>

            <div className="flex flex-col lg:flex-row items-center lg:items-start gap-4 lg:gap-6 mt-6 pagespeed-table">
              <UsabilityTable data={desktopMetrics} title="Metrics" />
              {desktopOpportunities.length > 0 && (
                <UsabilityTable
                  data={desktopOpportunities}
                  title="Opportunities"
                />
              )}
            </div>
          </ShowMoreSection>
        </div>
      )}

      {/* Core Web Vitals Mobile Section */}
      {pageSpeedMobile && pageSpeedMobile.core_web_vitals_mobile && (
        <div className="mb-6">
          <ShowMoreSection
            title="Google's Core Web Vitals - Mobile"
            description={pageSpeedMobile.core_web_vitals_mobile.description}
            passed={pageSpeedMobile.core_web_vitals_mobile.pass}
            importance={pageSpeedMobile.core_web_vitals_mobile.importance}
            recommendation={
              pageSpeedMobile.core_web_vitals_mobile.recommendation
            }
            icon={
              pageSpeedMobile.core_web_vitals_mobile.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
          >
            <div className="flex flex-col lg:flex-row items-center lg:items-start gap-4 lg:gap-6 pagespeed-table">
              <UsabilityTable data={coreWebVitalsMobile} />
            </div>
          </ShowMoreSection>
        </div>
      )}

      {/* Mobile PageSpeed Section */}
      {pageSpeedMobile && pageSpeedMobile.performance_mobile && (
        <div className="mb-6">
          <ShowMoreSection
            title="Page Speed - Mobile"
            description={pageSpeedMobile.performance_mobile.description}
            passed={pageSpeedMobile.performance_mobile.pass}
            importance={pageSpeedMobile.performance_mobile.importance}
            recommendation={pageSpeedMobile.performance_mobile.recommendation}
            icon={
              pageSpeedMobile.performance_mobile.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
          >
            <div className="mt-2 lg:mt-4 flex justify-center ">
              <ProgressChart
                score={mobileScore}
                title="Mobile PageSpeed Score"
                size="md"
                showGrade={false}
                progressStates={[
                  {
                    label: "Score",
                    value: mobileScore,
                    isNoColor: false,
                  },
                ]}
              />
            </div>

            <div className="flex flex-col lg:flex-row items-center lg:items-start gap-4 lg:gap-6 mt-6 pagespeed-table">
              <UsabilityTable data={mobileMetrics} title="Metrics" />
              {mobileOpportunities.length > 0 && (
                <UsabilityTable
                  data={mobileOpportunities}
                  title="Opportunities"
                />
              )}
            </div>
          </ShowMoreSection>
        </div>
      )}

      {/* No data message */}
      {(!pageSpeedDesktop ||
        (!pageSpeedDesktop.core_web_vitals_desktop &&
          !pageSpeedDesktop.performance_desktop)) &&
        (!pageSpeedMobile ||
          (!pageSpeedMobile.core_web_vitals_mobile &&
            !pageSpeedMobile.performance_mobile)) && (
          <div className="text-center py-4">
            <p className="text-secondary/60">No PageSpeed data available</p>
          </div>
        )}
    </div>
  );
}
