import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface BlogState {
  // Tag filtering state
  selectedTag: string | null;

  // Search state
  searchQuery: string;

  // Actions
  setSelectedTag: (tag: string | null) => void;
  clearSelectedTag: () => void;
  setSearchQuery: (query: string) => void;
  clearSearchQuery: () => void;

  // Navigation helper
  navigateToTagFilter: (tag: string) => void;
}

export const useBlogStore = create<BlogState>()(
  devtools(
    (set, get) => ({
      // Initial state
      selectedTag: null,
      searchQuery: "",

      // Actions
      setSelectedTag: (tag: string | null) => {
        set({ selectedTag: tag }, false, "setSelectedTag");
      },

      clearSelectedTag: () => {
        set({ selectedTag: null }, false, "clearSelectedTag");
      },

      setSearchQuery: (query: string) => {
        set({ searchQuery: query }, false, "setSearchQuery");
      },

      clearSearchQuery: () => {
        set({ searchQuery: "" }, false, "clearSearchQuery");
      },

      // Navigation helper that will be used when clicking tags from other pages
      navigateToTagFilter: (tag: string) => {
        // Set the tag in state and clear search
        set(
          { selectedTag: tag, searchQuery: "" },
          false,
          "navigateToTagFilter"
        );

        // Navigate to blog page
        if (typeof window !== "undefined") {
          window.location.href = "/blog";
        }
      },
    }),
    {
      name: "blog-store",
    }
  )
);
