name: Deploy

on:
  push:
    branches:
      - dev
      - main
      - BetaDev-Dev
  pull_request:
    branches:
      - dev
      - main
      - BetaDev-Dev

jobs:
  check_skip:
    runs-on: ubuntu-24.04
    outputs:
      skip: ${{ steps.skip_check.outputs.skip }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - id: skip_check
        run: |
          if git log -1 --pretty=%B | grep -iE '\[noci\]|noci'; then
            echo "skip=true" >> $GITHUB_OUTPUT
          else
            echo "skip=false" >> $GITHUB_OUTPUT
          fi

  deploy:
    needs: check_skip
    if: |
      needs.check_skip.outputs.skip != 'true' &&
      (
        github.ref == 'refs/heads/main' ||
        github.ref == 'refs/heads/BetaDev-Dev' ||
        github.ref == 'refs/heads/dev' ||
        github.base_ref == 'main' ||
        github.base_ref == 'BetaDev-Dev' ||
        github.base_ref == 'dev'
      )
    runs-on: ubuntu-24.04
    environment: ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}

    steps:
      - uses: actions/checkout@v4

      - name: Debug refs
        run: |
          echo "GITHUB REF: ${{ github.ref }}"
          echo "GITHUB BASE REF: ${{ github.base_ref }}"
          echo "EVENT NAME: ${{ github.event_name }}"

      - name: Generate known_hosts
        id: known_hosts
        run: |
          mkdir -p ~/.ssh
          KNOWN_HOSTS=$(ssh-keyscan -p 65533 -H *********** 2>/dev/null)
          echo "KNOWN_HOSTS<<EOF" >> $GITHUB_ENV
          echo "$KNOWN_HOSTS" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Install SSH key with webfactory action
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
        continue-on-error: true

      - name: Set up SSH key (fallback)
        env:
          SSH_AUTH_SOCK: /tmp/ssh_agent.sock
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          echo "SSH key file created with permissions:"
          ls -la ~/.ssh/id_rsa

          echo "Checking SSH key format..."
          if ! grep -q "BEGIN" ~/.ssh/id_rsa; then
            echo "SSH key does not contain BEGIN marker, trying to fix..."
            cat ~/.ssh/id_rsa | base64 --decode > ~/.ssh/id_rsa.decoded 2>/dev/null || echo "Not base64 encoded"
            if grep -q "BEGIN" ~/.ssh/id_rsa.decoded; then
              echo "Successfully decoded base64 key"
              mv ~/.ssh/id_rsa.decoded ~/.ssh/id_rsa
              chmod 600 ~/.ssh/id_rsa
            else
              rm -f ~/.ssh/id_rsa.decoded
              echo "Warning: SSH key format could not be fixed automatically"
            fi
          fi

          ssh-keyscan -p 65533 -H *********** >> ~/.ssh/known_hosts
          eval "$(ssh-agent -a $SSH_AUTH_SOCK)"
          ssh-add -v ~/.ssh/id_rsa || echo "Failed to add SSH key, but continuing..."

      - name: Deploy to Server
        env:
          DEPLOY_HOST: ***********
          DEPLOY_USER: seofront
          SSH_PORT: 65533
          SSH_AUTH_SOCK: /tmp/ssh_agent.sock
          GIT_SSH_COMMAND: "ssh -v -p 65533 -o StrictHostKeyChecking=no"
        run: |
          set -e
          set -o pipefail
          trap 'echo "Error occurred. Exiting..."; exit 1' ERR

          echo "🚀 Starting deployment to ${GITHUB_REF#refs/heads/} environment..."
          BRANCH_NAME=${GITHUB_REF#refs/heads/}

          ssh -v -p $SSH_PORT -t -o StrictHostKeyChecking=no -o IdentitiesOnly=yes -i ~/.ssh/id_rsa $DEPLOY_USER@$DEPLOY_HOST << EOF
            set -e
            echo "📦 Navigating to project directory..."
            cd /var/www/seoanalyser.com.au/Frontend/Front-End

            echo "📥 Pulling latest changes..."
            sudo git pull origin ${BRANCH_NAME} --force

            echo "📦 Installing/updating dependencies..."
            echo "📦 Node version: \$(node -v)"
            echo "📦 Bun version: \$(bun --version)"
            sudo bun install
            echo "📦 Dependencies installation completed."

            echo "🔨 Building the Next.js application..."
            sudo bun run build

            echo "🔄 Starting the Next.js application..."
            sudo pm2 stop seofront-app || true
            sudo pm2 start seofront-app

            echo "✅ Verifying service..."
            sleep 5
            if ! sudo pm2 status | grep -q "seofront-app.*online"; then
              echo "❌ Next.js application failed to start"
              exit 1
            fi
          EOF

      - name: Notify on failure
        if: failure()
        run: |
          echo "::error::Deployment failed! Check the logs for details."
