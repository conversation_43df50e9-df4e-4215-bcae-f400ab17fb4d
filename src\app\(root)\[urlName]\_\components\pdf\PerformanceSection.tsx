"use client";
import React from "react";
import {
  PerformanceAnalysis,
  PageSpeedAnalysis,
  PageSpeedMobileAnalysis,
} from "@/types/seoAnalyzerTypes";
import {
  SectionHeader,
  SectionScoreBox,
  DataRow,
  RecommendationCard,
} from "./BaseComponents";
import { SectionWatermark } from "./WatermarkComponents";
import PerformanceGaugeChart from "@/ui/charts/PerfomanceGaugeChart";
import CompressionPieChart from "../resultsBox/performance/CompressionPieChart";
import CompressionRates from "../resultsBox/performance/CompressionRates";

import { CheckCircleIcon, CrossIcon } from "@/ui/icons/general";

export interface PerformanceSectionProps {
  performanceData: PerformanceAnalysis;
  pagespeedData?: PageSpeedAnalysis | null;
  pagespeedMobileData?: PageSpeedMobileAnalysis | null;
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

export const PerformanceSection: React.FC<PerformanceSectionProps> = ({
  performanceData,
  pagespeedData,
  pagespeedMobileData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  // Calculate sections for the metrics display (keeping this for the passed/failed count)
  const sections = [
    performanceData.javascript_errors,
    performanceData.deprecated_html,
    performanceData.compression,
    performanceData.resource_count,
    performanceData.amp,
    performanceData.page_size,
    performanceData.inline_styles,
    performanceData.performance_timing,
    performanceData.http_protocol,
    performanceData.image_optimization,
    performanceData.minification,
  ];

  const validSections = sections.filter((section) => section !== undefined);
  const passedSections = validSections.filter(
    (section) => section?.pass === true
  );

  // Create data for the performance metrics display
  const performanceMetrics = [
    {
      name: "JavaScript Errors",
      pass: performanceData.javascript_errors?.pass,
      detail:
        performanceData.javascript_errors?.error_count !== undefined
          ? `${performanceData.javascript_errors.error_count} errors`
          : undefined,
    },
    {
      name: "Deprecated HTML",
      pass: performanceData.deprecated_html?.pass,
      detail:
        performanceData.deprecated_html?.count !== undefined
          ? `${performanceData.deprecated_html.count} elements`
          : undefined,
    },
    {
      name: "Compression",
      pass: performanceData.compression?.pass,
      detail:
        performanceData.compression?.compression_ratio !== undefined
          ? `${performanceData.compression.compression_ratio.toFixed(1)}% ratio`
          : undefined,
    },
    {
      name: "Resource Count",
      pass: performanceData.resource_count?.pass,
      detail:
        performanceData.resource_count?.total !== undefined
          ? `${performanceData.resource_count.total} resources`
          : undefined,
    },
    {
      name: "AMP",
      pass: performanceData.amp?.pass,
      detail: performanceData.amp?.is_amp_page ? "AMP enabled" : "Not AMP",
    },
    {
      name: "Page Size",
      pass: performanceData.page_size?.pass,
      detail:
        performanceData.page_size?.total_estimated_size_mb !== undefined
          ? `${performanceData.page_size.total_estimated_size_mb.toFixed(2)} MB`
          : undefined,
    },
    {
      name: "Inline Styles",
      pass: performanceData.inline_styles?.pass,
      detail:
        performanceData.inline_styles?.inline_style_percentage !== undefined
          ? `${performanceData.inline_styles.inline_style_percentage.toFixed(
              1
            )}% of elements`
          : undefined,
    },
    {
      name: "Performance Timing",
      pass: performanceData.performance_timing?.pass,
      detail:
        performanceData.performance_timing?.time_to_first_byte_s !== undefined
          ? `TTFB: ${performanceData.performance_timing.time_to_first_byte_s.toFixed(
              2
            )}s`
          : undefined,
    },
    {
      name: "HTTP Protocol",
      pass: performanceData.http_protocol?.pass,
      detail: performanceData.http_protocol?.protocol_used,
    },
    {
      name: "Image Optimisation",
      pass: performanceData.image_optimization?.pass,
      detail:
        performanceData.image_optimization?.total_images !== undefined
          ? `${performanceData.image_optimization.total_images} images`
          : undefined,
    },
    {
      name: "Minification",
      pass: performanceData.minification?.pass,
      detail:
        performanceData.minification?.unminified_js_count !== undefined &&
        performanceData.minification?.unminified_css_count !== undefined
          ? `${
              performanceData.minification.unminified_js_count +
              performanceData.minification.unminified_css_count
            } unminified files`
          : undefined,
    },
  ];

  return (
    <div
      className="mb-8 print-section relative"
      data-watermark={brand_website || brand_name || "SEO ANALYSER"}
    >
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => onImageLoad?.("sectionLogoPerf")}
        onLogoError={() => onImageError?.("sectionLogoPerf")}
        sectionId="performance-details"
      />

      <SectionHeader
        title="Performance Audit"
        brandName={brand_name}
        brandWebsite={brand_website}
      />

      {/* Section Score Box */}
      {performanceData.total_score && (
        <SectionScoreBox
          scoreGrade={performanceData.total_score}
          title="Performance Score"
          description={
            performanceData.overall_description ||
            "Analysis of your website's performance metrics including loading speed, page size optimization, server response times, and overall performance factors that impact user experience and SEO rankings."
          }
        />
      )}

      {/* Performance Metrics */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <h4 className="font-semibold text-gray-800">Performance Metrics</h4>
          <div className="flex items-center gap-4 mt-2 md:mt-0">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
              <span className="text-xs text-gray-600">
                Passed: {passedSections.length}
              </span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
              <span className="text-xs text-gray-600">
                Failed: {validSections.length - passedSections.length}
              </span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {performanceMetrics.map((metric, index) => (
            <div
              key={index}
              className="flex items-center p-3 rounded-lg border border-gray-200"
            >
              <div className="mr-3">
                {metric.pass ? (
                  <CheckCircleIcon className="w-6 h-6 text-green-500" />
                ) : (
                  <CrossIcon className="w-6 h-6 text-red-500" />
                )}
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-gray-800 font-medium">
                  {metric.name}
                </span>
                {metric.detail && (
                  <span className="text-xs text-gray-600">{metric.detail}</span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Detailed Performance Analysis Sections */}
      <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
        {performanceData.minification && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Code Minification
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Total CSS Files"
                value={performanceData.minification.total_css || 0}
              />
              <DataRow
                label="Unminified CSS"
                value={performanceData.minification.unminified_css_count || 0}
              />
              <DataRow
                label="Total JS Files"
                value={performanceData.minification.total_js || 0}
              />
              <DataRow
                label="Unminified JS"
                value={performanceData.minification.unminified_js_count || 0}
              />
              <DataRow
                label="Status"
                value={
                  performanceData.minification.pass
                    ? "✓ Good"
                    : "⚠ Needs Improvement"
                }
              />
            </div>

            {/* Minification Recommendation */}
            {performanceData.minification.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={performanceData.minification.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Resource Count */}
        {performanceData.resource_count && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Resource Count
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Total Resources"
                value={performanceData.resource_count.total || 0}
              />
              <DataRow
                label="CSS Files"
                value={performanceData.resource_count.css || 0}
              />
              <DataRow
                label="JS Files"
                value={performanceData.resource_count.js || 0}
              />
              <DataRow
                label="Image Files"
                value={performanceData.resource_count.images || 0}
              />
              <DataRow
                label="Status"
                value={
                  performanceData.resource_count.pass
                    ? "✓ Reasonable"
                    : "⚠ Too Many Resources"
                }
              />
            </div>

            {/* Resource Count Recommendation */}
            {performanceData.resource_count.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={performanceData.resource_count.recommendation}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Detailed Performance Analysis Sections */}
      <div className="mt-8 grid grid-cols-2 lg:grid-cols-2 gap-6">
        {/* Deprecated HTML Section */}
        {performanceData.deprecated_html && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Deprecated HTML
              </h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Identifies outdated HTML elements that may not be supported or
              render correctly in modern browsers.
            </p>

            <div className="mb-4">
              <div className="text-lg font-semibold text-gray-800">
                Deprecated Elements Count:{" "}
                {performanceData.deprecated_html.count || 0}
              </div>
            </div>

            {performanceData.deprecated_html.elements &&
              performanceData.deprecated_html.elements.length > 0 && (
                <div className="mt-4">
                  <h4 className="font-medium text-gray-700 mb-2">
                    Deprecated Elements Found:
                  </h4>
                  <div className="space-y-3">
                    {performanceData.deprecated_html.elements
                      .slice(0, 3)
                      .map((element: any, index: number) => (
                        <div
                          key={index}
                          className="p-3 rounded-md border border-gray-200 bg-gray-50"
                        >
                          <div className="flex items-center mb-2">
                            <span className="inline-block px-2 py-1 bg-red-100 text-red-700 text-xs font-bold rounded mr-2">
                              {element.tag}
                            </span>
                            <span className="text-sm text-gray-700">
                              Deprecated HTML Tag
                            </span>
                          </div>
                          <div className="text-xs text-gray-600">
                            <strong>Content:</strong> {element.content}
                          </div>
                          <div className="mt-2">
                            <div className="text-xs text-gray-600 mb-1">
                              <strong>HTML:</strong>
                            </div>
                            <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                              {element.html}
                            </pre>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}

            {performanceData.deprecated_html.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={
                    performanceData.deprecated_html.recommendation
                  }
                />
              </div>
            )}
          </div>
        )}

        {/* Compression Section */}
        {performanceData.compression && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">Compression</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Checks if server-side compression (like gzip or Brotli) is enabled
              for the HTML document, reducing its transfer size.
            </p>

            <div className="space-y-2">
              <div className="text-sm">
                <strong>Compression Type:</strong>{" "}
                {performanceData.compression.compression_type || "Unknown"}
              </div>
              <div className="text-sm">
                <strong>Original Size:</strong>{" "}
                {performanceData.compression.size_mb?.toFixed(2) || 0} MB
              </div>
              <div className="text-sm">
                <strong>Compressed Size:</strong>{" "}
                {performanceData.compression.compressed_size_mb?.toFixed(2) ||
                  0}{" "}
                MB
              </div>
              <div className="text-sm">
                <strong>Compression Ratio:</strong>{" "}
                {performanceData.compression.compression_ratio?.toFixed(1) || 0}
                %
              </div>
            </div>

            {performanceData.compression.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={performanceData.compression.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* AMP Section */}
        {performanceData.amp && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                AMP (Accelerated Mobile Pages)
              </h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Checks if the page is an AMP page itself or links to a separate
              AMP version.
            </p>

            <div className="space-y-2">
              <div className="text-sm">
                <strong>Is AMP Page:</strong>{" "}
                {performanceData.amp.is_amp_page ? "Yes" : "No"}
              </div>
              <div className="text-sm">
                <strong>Has AMP Link:</strong>{" "}
                {performanceData.amp.has_amp_link ? "Yes" : "No"}
              </div>
              {performanceData.amp.amp_url && (
                <div className="text-sm">
                  <strong>AMP URL:</strong> {performanceData.amp.amp_url}
                </div>
              )}
            </div>

            {performanceData.amp.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={performanceData.amp.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Enhanced Page Size Section */}
        {performanceData.page_size && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">Page Size</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Provides a rough estimate of the total page size (HTML + estimated
              resources) based on the number of assets linked.
            </p>

            <div className="mb-6">
              <div className="text-lg font-semibold text-gray-800">
                Total Size:{" "}
                {performanceData.page_size.total_estimated_size_mb?.toFixed(
                  2
                ) || 0}{" "}
                MB ({performanceData.page_size.size_category || "Unknown"})
              </div>
            </div>

            {/* Display CompressionPieChart and CompressionRates side by side */}
            <div className="mb-6 flex flex-col lg:flex-row gap-6">
              <div className="lg:w-1/2 flex mt-3">
                <CompressionRates
                  pageSizeData={performanceData.page_size}
                  className="w-full"
                />
              </div>
              <div className="lg:w-1/2 flex justify-center">
                <CompressionPieChart
                  pageSizeData={performanceData.page_size}
                  className="max-w-md"
                />
              </div>
            </div>

            {performanceData.page_size.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={performanceData.page_size.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Inline Styles Section */}
        {performanceData.inline_styles && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">Inline Styles</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Analyses the usage of inline style attributes within HTML
              elements.
            </p>

            <div className="space-y-2 mb-4">
              <div className="text-sm">
                <strong>Elements with Inline Styles:</strong>{" "}
                {performanceData.inline_styles.elements_with_style || 0} of{" "}
                {performanceData.inline_styles.total_elements || 0} (
                {performanceData.inline_styles.inline_style_percentage?.toFixed(
                  1
                ) || 0}
                %)
              </div>
              <div className="text-sm">
                <strong>Total Style Size:</strong>{" "}
                {performanceData.inline_styles.total_style_size_kb?.toFixed(
                  1
                ) || 0}{" "}
                KB
              </div>
            </div>

            {performanceData.inline_styles.most_common_properties &&
              Object.keys(performanceData.inline_styles.most_common_properties)
                .length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium text-gray-700 mb-2">
                    Most Common Properties:
                  </h4>
                  <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                    {Object.entries(
                      performanceData.inline_styles.most_common_properties
                    )
                      .slice(0, 5)
                      .map(([property, count], index) => (
                        <li key={index}>
                          {property}: {count} occurrences
                        </li>
                      ))}
                  </ul>
                </div>
              )}

            {performanceData.inline_styles.style_examples &&
              performanceData.inline_styles.style_examples.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium text-gray-700 mb-2">Examples:</h4>
                  <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                    {performanceData.inline_styles.style_examples
                      .slice(0, 3)
                      .map((example, index) => (
                        <li key={index}>
                          <code className="bg-gray-100 px-1 rounded">
                            {example.tag}
                          </code>
                          :{" "}
                          <code className="bg-gray-100 px-1 rounded">
                            {example.style}
                          </code>
                        </li>
                      ))}
                  </ul>
                </div>
              )}

            {performanceData.inline_styles.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={performanceData.inline_styles.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Performance Timing Section */}
        {performanceData.performance_timing && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Performance Timing
              </h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Measures the Time To First Byte (TTFB), indicating server
              responsiveness.
            </p>

            <div className="mb-4">
              <div className="text-sm">
                <strong>Time to First Byte (TTFB):</strong>{" "}
                {performanceData.performance_timing.time_to_first_byte_s?.toFixed(
                  2
                ) || 0}
                s
              </div>
            </div>

            <div className="flex justify-center mb-4">
              <PerformanceGaugeChart
                title="Time to First Byte"
                time={performanceData.performance_timing.time_to_first_byte_s}
                max={10}
                green_time={0.8} // Good: <= 0.8s
                warning_time={1.8} // Needs improvement: <= 1.8s
                red_time={1.8} // Poor: > 1.8s
              />
            </div>

            {performanceData.performance_timing.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={
                    performanceData.performance_timing.recommendation
                  }
                />
              </div>
            )}
          </div>
        )}

        {/* HTTP Protocol Section */}
        {performanceData.http_protocol && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">HTTP Protocol</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Checks the HTTP protocol version used (HTTP/1.1, HTTP/2) and looks
              for HTTP/3 support via the Alt-Svc header.
            </p>

            <div className="space-y-2">
              <div className="text-sm">
                <strong>Protocol Used:</strong>{" "}
                {performanceData.http_protocol.protocol_used || "Unknown"}
              </div>
              <div className="text-sm">
                <strong>Supports HTTP/2:</strong>{" "}
                {performanceData.http_protocol.supports_http2 ? "Yes" : "No"}
              </div>
              <div className="text-sm">
                <strong>Supports HTTP/3:</strong>{" "}
                {performanceData.http_protocol.supports_http3 ? "Yes" : "No"}
              </div>
              {performanceData.http_protocol.alt_svc_header && (
                <div className="text-sm">
                  <strong>Alt-Svc Header:</strong>{" "}
                  {performanceData.http_protocol.alt_svc_header}
                </div>
              )}
            </div>

            {performanceData.http_protocol.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={performanceData.http_protocol.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Minification Section */}
        {performanceData.minification && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">Minification</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Checks if linked JavaScript and CSS filenames suggest they are
              minified (contain '.min.').
            </p>

            {/* JavaScript Section */}
            <div className="mb-6">
              <h4 className="font-medium text-gray-700 mb-3">JavaScript:</h4>
              <div className="space-y-2 mb-3">
                <div className="text-sm">
                  <strong>Total JS Files:</strong>{" "}
                  {performanceData.minification.total_js || 0}
                </div>
                <div className="text-sm">
                  <strong>Unminified JS Files:</strong>{" "}
                  {performanceData.minification.unminified_js_count || 0}
                </div>
              </div>

              {performanceData.minification.unminified_js_samples &&
                performanceData.minification.unminified_js_samples.length >
                  0 && (
                  <div>
                    <h5 className="font-medium text-gray-700 mb-2">
                      Unminified JS Examples:
                    </h5>
                    <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                      {performanceData.minification.unminified_js_samples
                        .slice(0, 3)
                        .map((file, index) => (
                          <li key={index} className="break-all">
                            {file}
                          </li>
                        ))}
                    </ul>
                  </div>
                )}
            </div>

            {/* CSS Section */}
            <div className="mb-4">
              <h4 className="font-medium text-gray-700 mb-3">CSS:</h4>
              <div className="space-y-2 mb-3">
                <div className="text-sm">
                  <strong>Total CSS Files:</strong>{" "}
                  {performanceData.minification.total_css || 0}
                </div>
                <div className="text-sm">
                  <strong>Unminified CSS Files:</strong>{" "}
                  {performanceData.minification.unminified_css_count || 0}
                </div>
              </div>

              {performanceData.minification.unminified_css_samples &&
                performanceData.minification.unminified_css_samples.length >
                  0 && (
                  <div>
                    <h5 className="font-medium text-gray-700 mb-2">
                      Unminified CSS Examples:
                    </h5>
                    <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                      {performanceData.minification.unminified_css_samples
                        .slice(0, 3)
                        .map((file, index) => (
                          <li key={index} className="break-all">
                            {file}
                          </li>
                        ))}
                    </ul>
                  </div>
                )}
            </div>

            {performanceData.minification.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={performanceData.minification.recommendation}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Performance Recommendations - Moved outside grid for full width */}
      <div className="mt-8">
        <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
          Performance Recommendations
        </h3>
        <div className="grid grid-cols-1 gap-4 recommendations-grid">
          {/* Page Size Recommendations */}
          {performanceData.page_size?.recommendation && (
            <RecommendationCard
              recommendation={performanceData.page_size.recommendation}
            />
          )}

          {/* Compression Recommendations */}
          {performanceData.compression?.recommendation && (
            <RecommendationCard
              recommendation={performanceData.compression.recommendation}
            />
          )}

          {/* JavaScript Errors Recommendations */}
          {performanceData.javascript_errors?.recommendation && (
            <RecommendationCard
              recommendation={performanceData.javascript_errors.recommendation}
            />
          )}

          {/* AMP Recommendations */}
          {performanceData.amp?.recommendation && (
            <RecommendationCard
              recommendation={performanceData.amp.recommendation}
            />
          )}

          {/* Inline Styles Recommendations */}
          {performanceData.inline_styles?.recommendation && (
            <RecommendationCard
              recommendation={performanceData.inline_styles.recommendation}
            />
          )}

          {/* Performance Timing Recommendations */}
          {performanceData.performance_timing?.recommendation && (
            <RecommendationCard
              recommendation={performanceData.performance_timing.recommendation}
            />
          )}

          {/* General Performance Recommendation if no specific ones */}
          {!performanceData.page_size?.recommendation &&
            !performanceData.compression?.recommendation &&
            !performanceData.javascript_errors?.recommendation &&
            !performanceData.amp?.recommendation &&
            !performanceData.inline_styles?.recommendation &&
            !performanceData.performance_timing?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: "Your website's performance appears to be well optimized. Continue monitoring page load times and resource optimization.",
                  priority: "Low",
                }}
              />
            )}
        </div>
      </div>
    </div>
  );
};
