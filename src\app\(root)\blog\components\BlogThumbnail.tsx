import Image from "next/image";
import DateHolder from "./DateHolder";

interface BlogThumbnailProps {
  image: string;
  date: Date;
  author: string;
}

const BlogThumbnail: React.FC<BlogThumbnailProps> = ({
  image,
  date,
  author,
}) => {
  return (
    <div className=" overflow-hidden rounded-2xl relative w-full h-[216px] lg:h-[400px]">
      <Image
        src={image}
        alt="Blog Thumbnail"
        width={667}
        height={1000}
        className="w-full object-cover object-center rounded-2xl"
      />
      <div className="p-6 absolute top-0 left-0 w-full h-full bg-gradient-to-b from-[#00000000] to-[#00000080] rounded-2xl">
        <div className="flex items-start justify-between">
          <DateHolder date={date} />
          <div className="bg-white flex flex-col gap-2 rounded-lg px-4 py-2 text-[12px]">
            <div>
              Written by
              <b>{author}</b>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogThumbnail;
