"use client";
import { useState, useCallback } from "react";
import Image from "next/image";
import StripeIcon from "@/ui/icons/payment/StripeIcon";
import { PricingPlan } from "@/services/pricingService";
import paymentService from "@/services/paymentService";
import { useRouter } from "next/navigation";

// Define types for billing period
type BillingPeriod = "monthly" | "annually";

// Define types for the White Label payment method
type PaymentMethod = "paypal" | "credit-card" | "crypto" | "stripe";

interface PaymentMethodStepProps {
  onNext: (paymentMethod: PaymentMethod) => void;
  onBack: () => void;
  selectedPlan: string;
  billingPeriod: BillingPeriod;
  apiPricingData: PricingPlan[];
  premiumPlanData: PricingPlan[];
  initialPaymentMethod: PaymentMethod | null;
  taskId?: string;
}

export default function PaymentMethodStep({
  onNext,
  onBack,
  selectedPlan,
  billingPeriod,
  apiPricingData,
  premiumPlanData,
  initialPaymentMethod,
  taskId,
}: PaymentMethodStepProps) {
  // Initialize router
  const router = useRouter();

  // State for the selected payment method - always Stripe
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>("stripe");

  // State for loading
  const [isLoading, setIsLoading] = useState(false);

  // State for errors
  const [error, setError] = useState<string | null>(null);

  // Handle next button click
  const handleNextClick = useCallback(async () => {
    setError(null);
    setIsLoading(true);

    try {
      // Get the plan ID from the pricing data
      const pricingData =
        selectedPlan === "premium" ? premiumPlanData : apiPricingData;
      const periodType = billingPeriod === "monthly" ? "monthly" : "yearly";
      const plan = pricingData.find((p) => p.period === periodType);

      if (!plan) {
        throw new Error("Selected plan not found");
      }

      // Create payment session with the API
      const response = await paymentService.createPaymentSession({
        plan_id: plan.id,
        task_id: taskId || "",
      });

      // Log the payment session response
      console.log("Payment session created:", response);

      // Handle the new API response format which includes checkout_url
      if (response.checkout_url) {
        // Redirect to Stripe checkout using the checkout_url
        window.location.href = response.checkout_url;
        return; // Stop execution after redirect
      } else if (response.url) {
        // Fallback to the old format for backward compatibility
        window.location.href = response.url;
        return; // Stop execution after redirect
      }

      // If no redirect URL is provided, proceed to next step
      onNext(paymentMethod);
    } catch (error: any) {
      console.error("Payment processing error:", error);

      // Check if it's an authentication error
      if (error.response && error.response.status === 401) {
        setError("Authentication required. Please log in to continue with your payment.");
      } else {
        setError("Payment processing failed. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  }, [
    selectedPlan,
    billingPeriod,
    apiPricingData,
    premiumPlanData,
    taskId,
    onNext,
  ]);

  // Get price information based on selected plan and billing period
  const getPriceInfo = useCallback(() => {
    // Get the appropriate pricing data based on the selected plan
    const pricingData =
      selectedPlan === "premium" ? premiumPlanData : apiPricingData;
    const periodType = billingPeriod === "monthly" ? "monthly" : "yearly";

    // Find the plan with the matching period
    const plan = pricingData.find((p) => p.period === periodType);

    if (!plan) {
      // Fallback to default pricing if API data is not available
      const defaultPrice =
        selectedPlan === "premium"
          ? billingPeriod === "monthly"
            ? 79
            : 758
          : billingPeriod === "monthly"
          ? 28
          : 288;

      return billingPeriod === "annually"
        ? `$${(defaultPrice / 12).toFixed(
            2
          )}/month (billed annually as $${defaultPrice.toFixed(2)})`
        : `$${defaultPrice.toFixed(2)}/month`;
    }

    // Parse the price from the API data
    const price = parseFloat(plan.price.replace("$", ""));

    return billingPeriod === "annually"
      ? `$${(price / 12).toFixed(2)}/month (billed annually as $${price.toFixed(
          2
        )})`
      : `$${price.toFixed(2)}/month`;
  }, [selectedPlan, billingPeriod, apiPricingData, premiumPlanData]);

  return (
    <div className="p-4 overflow-y-auto">
      <h2 className="text-xl font-bold text-secondary mb-4">Payment Method</h2>
      <p className="text-secondary mb-6">Proceed with Stripe secure payment.</p>

      <div className="space-y-4">
        <div className="border border-primary bg-primary/5 rounded-lg p-4">
          <div className="flex items-center">
            <div className="w-8 h-8 mr-3 flex items-center justify-center">
              <div className="w-5 h-5 bg-primary rounded-full"></div>
            </div>
            <div className="flex items-center">
              <span className="text-lg font-medium text-secondary mr-2">
                Stripe
              </span>
              <StripeIcon className="w-6 h-6 text-primary" />
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 bg-primary/10 p-4 rounded-lg">
        <div className="flex justify-between items-center">
          <span className="text-secondary font-medium">Selected Plan:</span>
          <span className="text-secondary font-bold">
            {selectedPlan === "basic"
              ? "White Label"
              : "White Label & Embedding"}
          </span>
        </div>
        <div className="flex justify-between items-center mt-2">
          <span className="text-secondary font-medium">Price:</span>
          <span className="text-primary font-bold">{getPriceInfo()}</span>
        </div>
      </div>

      {error && (
        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-lg">
          {error}
        </div>
      )}

      <div className="mt-6 flex justify-between">
        <button
          onClick={onBack}
          className="btn btn--outline px-6 py-2"
          disabled={isLoading}
        >
          Back
        </button>
        <button
          onClick={handleNextClick}
          className={`btn btn--primary px-6 py-2 ${
            isLoading ? "opacity-70 cursor-not-allowed" : ""
          }`}
          disabled={isLoading}
        >
          {isLoading ? (
            <div className="flex items-center">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Processing...
            </div>
          ) : (
            "Complete Payment"
          )}
        </button>
      </div>
    </div>
  );
}
