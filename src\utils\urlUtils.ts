/**
 * Utility functions for URL manipulation and formatting
 */

/**
 * Removes protocol (http/https) and www. from a URL for display purposes
 * @param url - The URL to clean
 * @returns The cleaned URL without protocol and www.
 * 
 * @example
 * removeWwwFromUrl("https://www.example.com") // returns "example.com"
 * removeWwwFromUrl("http://example.com") // returns "example.com"
 * removeWwwFromUrl("www.example.com") // returns "example.com"
 * removeWwwFromUrl("example.com") // returns "example.com"
 */
export const removeWwwFromUrl = (url: string): string => {
  if (!url) return '';
  
  return url
    .replace(/^https?:\/\//, '') // Remove http:// or https://
    .replace(/^www\./, ''); // Remove www.
};

/**
 * Extracts the domain name from a URL (removes protocol, www, and path)
 * @param url - The URL to extract domain from
 * @returns The domain name only
 * 
 * @example
 * extractDomain("https://www.example.com/path") // returns "example.com"
 * extractDomain("http://subdomain.example.com") // returns "subdomain.example.com"
 */
export const extractDomain = (url: string): string => {
  if (!url) return '';
  
  return url
    .replace(/^https?:\/\//, '') // Remove protocol
    .replace(/^www\./, '') // Remove www.
    .split('/')[0]; // Remove path
};

/**
 * Formats a URL for display in SEO audit components
 * This is the main function to use for displaying URLs in the audit
 * @param url - The URL to format
 * @returns The formatted URL for display
 */
export const formatUrlForDisplay = (url: string): string => {
  return removeWwwFromUrl(url);
};
