import { EditReportIcon } from "@/ui/icons/action";
import OverviewDetails from "./OverviewDetails";
import Image from "next/image";

export default function OverviewTop() {
  return (
    <div className="w-full flex flex-col-reverse lg:flex-row items-center gap-6 lg:gap-16">
      <OverviewDetails
        btnTxt="Generate My Report"
        title="White Label & Advanced SEO Report – Ready to Share"
        description={
          <p className="text-justify text-sm lg:text-base font-medium text-secondary mt-4">
            Impress clients and scale your brand with fully customizable SEO
            reports. Instantly generate professional PDFs, branded with your
            logo and styled to match your visual identity.
            <br />
            Designed to communicate what matters most, clarity, insights, and
            impact your clients will understand.
          </p>
        }
        icon={<EditReportIcon />}
        className="w-full lg:w-[498px] lg:min-h-[400px]"
      />
      <div className="lg:flex-1 w-full pr-2 lg:pr-0 flex justify-center">
        <Image
          src="/images/overview-top.svg"
          alt="Overview Top"
          width={680}
          height={580}
          className="w-full"
        />
      </div>
    </div>
  );
}
