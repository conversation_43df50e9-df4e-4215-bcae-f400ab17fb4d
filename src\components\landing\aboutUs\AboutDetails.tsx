import { ChartIcon, MonitorIcon, OrderCheckIcon } from "@/ui/icons/general";

export default function AboutDetails() {
  return (
    <div className="flex-1 max-w-[609px] 2xl:max-w-none">
      <div className="mb-8 space-y-4">
        <div className="">
          <h4 className="text-3xl font-black text-secondary font-heading leading-tight">
            About Us
          </h4>
        </div>

        <div className="">
          <p className="text-lg font-semibold text-secondary leading-relaxed">
            We Help You Climb the Rankings
          </p>
        </div>

        <div className="">
          <p className="text-sm lg:text-base font-medium text-secondary leading-loose text-justify space-y-3">
            <span className="block ">
              <strong>At SEO Analyser</strong>, we believe that every business
              deserves to be seen. That&rsquo;s why we built a tool that
              simplifies SEO audits and makes website optimisation accessible,
              understandable, and actionable.
            </span>

            <span className="block ">
              We scan your site for key ranking factors, Technology issues,
              content gaps, and performance flaws, then deliver clear,
              prioritized recommendations that anyone can follow. No buzzwords.
              Just real insights to grow your traffic and conversions.
            </span>

            <span className="block ">
              Whether you're an agency, entrepreneur, or in-house marketer, our
              platform gives you the tools to uncover opportunities and outrank
              the competition faster.
            </span>
          </p>
        </div>
      </div>

      {/* <div className="w-full grid grid-cols-2 lg:flex lg:items-center gap-y-4 lg:gap-7 [&>*:nth-child(2)]:border-l [&>*:nth-child(3)]:col-span-2 [&>*:nth-child(3)]:border-t">
        <StateCard
          label="Lorem Ipsum"
          value={77}
          icon={<MonitorIcon className="w-10 h-10" />}
        />
        <StateCard
          label="Lorem Ipsum"
          value={77}
          icon={<ChartIcon className="w-10 h-10" />}
        />
        <StateCard
          label="Lorem Ipsum"
          value={77}
          icon={<OrderCheckIcon className="w-10 h-10" />}
        />
      </div> */}
    </div>
  );
}

type StateCardProps = {
  value: number;
  icon: React.ReactNode;
  label: string;
};

function StateCard({ icon, label, value }: StateCardProps) {
  return (
    <div className="group flex items-center justify-center gap-7 whitespace-nowrap border-light-gray lg:!border-t-0">
      <div className="w-px h-[110px] lg:h-[87px] bg-light-gray hidden lg:block group-first:hidden"></div>
      <div className="flex flex-col gap-2 items-center py-4 px-8 lg:px-9.5">
        <div className="text-secondary">{icon}</div>
        <div className="text-2xl text-secondary font-semibold">+ {value}</div>
        <div className="text-secondary font-semibold">{label}</div>
      </div>
    </div>
  );
}
