import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";
import BlogPic from "../../blog.jpg";
import DateHolder from "../../components/DateHolder";
import SearchInput from "../../components/SearchInput";
import CategoryList from "../../components/CategoryList";
import BlogPostTags from "../../components/BlogPostTags";
import blogService, { BlogPost } from "@/services/blogService";
import { ArrowRigthIcon } from "@/ui/icons/navigation";
import "../../blogStyle.css";
// Define the props for the page component
interface BlogPostPageProps {
  params: Promise<{
    category: string;
    slug: string;
  }>;
}

// Generate metadata for the page
export async function generateMetadata({
  params,
}: BlogPostPageProps): Promise<Metadata> {
  try {
    const { slug } = await params;
    const blogPost = await blogService.getBlogPostBySlug(slug);

    // Use the snippet for the description if available, otherwise extract from body
    let description = blogPost.snippet || "";

    if (!description && blogPost.body) {
      // Strip HTML tags and limit to 160 characters for meta description
      description = blogPost.body
        .replace(/<[^>]*>?/gm, "") // Remove HTML tags
        .trim()
        .substring(0, 160);

      if (blogPost.body.length > 160) description += "...";
    }

    return {
      title: blogPost.title,
      description: description || "Read our latest blog post",
      openGraph: {
        title: blogPost.title,
        description: description || "Read our latest blog post",
        type: "article",
        publishedTime: new Date(
          blogPost.publish_timestamp * 1000
        ).toISOString(),
        authors: [blogPost.author.display_name],
        tags: blogPost.tags,
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Blog Post",
      description: "Read our latest blog post",
    };
  }
}

// The main page component
export default async function BlogPostPage({ params }: BlogPostPageProps) {
  try {
    const { category, slug } = await params;
    const blogPost = await blogService.getBlogPostBySlug(slug);

    // Verify that the URL matches the expected format
    // The URL should be /blog/category/slug
    // If the category in the URL doesn't match the category from the API, we'll still show the post
    // but we might want to implement a redirect in the future

    // Log the blog post data for debugging
    console.log("Blog post data:", {
      title: blogPost.title,
      author: blogPost.author.display_name,
      bodyLength: blogPost.body?.length || 0,
      hasBody: !!blogPost.body,
      bodyPreview: blogPost.body?.substring(0, 100) || "No body content",
    });

    return (
      <div className="w-full lg:mt-[84px] bg-white py-6 pt-8 rounded-2xl lg:px-2 mt-8 mb-16 container flex justify-center blog-single-page">
        <div className="flex flex-col w-full gap-6 max-w-5xl">
          {/* Back to Blog Button - Positioned at the top for better UX */}
          <div className="flex justify-start">
            <Link
              href="/blog"
              className="bg-primary text-white hover:bg-primary/85 px-4 py-2 rounded-lg flex flex-row hover:shadow-md justify-center items-center"
            >
              <ArrowRigthIcon className="rotate-180 w-6 h-6 transition-transform duration-300 group-hover:-translate-x-3" />
              Back to Blog
            </Link>
          </div>

          {/* Sidebar */}
          {/* <div className="lg:col-span-1 flex flex-col gap-4">
            <SearchInput initialQuery="" />
            <CategoryList />
          </div> */}

          {/* Main content */}
          <div className="lg:col-span-3 font-semibold">
            {/* Breadcrumb Navigation */}
            <div className="mb-6">
              <div className="flex flex-wrap gap-2 text-xs lg:text-base mb-4">
                <Link
                  href="/blog"
                  className="text-primary hover:underline inline-block transition-colors duration-200"
                >
                  Blog
                </Link>
                <span className="text-gray-400">/</span>
                <Link
                  href={`/blog/${category}`}
                  className="text-primary hover:underline inline-block capitalize transition-colors duration-200"
                >
                  {decodeURIComponent(category)}
                </Link>
                <span className="text-gray-400">/</span>
                <span className="text-gray-600 truncate">{blogPost.title}</span>
              </div>
              <div className="mt-6 ">
                <h1 className="blogTitle   ">{blogPost.title}</h1>
              </div>
            </div>

            {/* Blog thumbnail */}
            <div className="overflow-hidden rounded-2xl relative w-full aspect-[1138/490] mb-6">
              <Image
                src={blogPost.cover_image || BlogPic}
                alt={blogPost.title}
                width={1138}
                height={490}
                className="w-full h-full object-contain object-center rounded-2xl"
                unoptimized={blogPost.cover_image ? true : false} // Don't optimize external images
              />
              <div className="p-6 absolute top-0 left-0 w-full h-full bg-gradient-to-b from-[#00000000] to-[#00000080] rounded-2xl">
                <div className="flex items-start gap-2 sm:gap-3 md:gap-4 justify-between">
                  <DateHolder date={blogPost.publish_timestamp} />
                  <div className="bg-white flex flex-col gap-2 rounded-lg px-4 py-2 text-[10px] lg:text-[12px]">
                    <div>
                      Written by <b>{blogPost.author.display_name}</b>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Tags */}
            <BlogPostTags tags={blogPost.tags} />

            {/* Blog content */}
            <article
              className="ck-content prose prose-lg max-w-none text-secondary prose-headings:text-secondary prose-headings:font-bold prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-img:rounded-lg leading-relaxed pb-4"
              dangerouslySetInnerHTML={{ __html: blogPost.body }}
            ></article>

            {/* Similar posts */}
            {blogPost.similar_posts && blogPost.similar_posts.length > 0 && (
              <div className="mt-12">
                <h2 className="text-secondary font-black text-xl mb-6">
                  Similar Posts
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {blogPost.similar_posts.map((similarPost) => {
                    // Extract category from the URL if available
                    // Default to the current category if URL is not available
                    let postCategory = category;

                    // If the similar post has a URL, try to extract the category from it
                    if (similarPost.url) {
                      const urlParts = similarPost.url
                        .split("/")
                        .filter(Boolean);
                      if (urlParts.length > 1) {
                        postCategory = urlParts[1];
                      }
                    }

                    return (
                      <Link
                        key={similarPost.id}
                        href={`/blog/${postCategory}/${similarPost.slug}`}
                        className="p-4 border border-gray-200 rounded-lg hover:border-primary transition-colors"
                      >
                        <h3 className="text-secondary font-bold mb-2">
                          {similarPost.title}
                        </h3>
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <span>By {similarPost.author.display_name}</span>
                          <span>
                            {new Date(
                              similarPost.publish_timestamp * 1000
                            ).toLocaleDateString()}
                          </span>
                        </div>
                      </Link>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error fetching blog post:", error);

    // Return error state
    return (
      <div className="w-full mt-8 lg:mt-[84px] container">
        <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6">
          {/* Back to Blog Button - Error State */}
          <div className="lg:col-span-4 flex justify-start mb-4">
            <Link
              href="/blog"
              className="btn btn--primary__outline group transition-all duration-300 hover:shadow-md"
            >
              <ArrowRigthIcon className="rotate-180 w-4 h-4 transition-transform duration-300 group-hover:-translate-x-1" />
              Back to Blog
            </Link>
          </div>

          <div className="lg:col-span-1 flex flex-col gap-4">
            <SearchInput defaultValue="" />
            <CategoryList categories={[]} />
          </div>
          <div className="lg:col-span-3">
            <div className="p-6 py-18 bg-red-50 border border-red-300 rounded-lg">
              <h2 className="text-xl font-bold text-red-700 mb-2">
                Blog Post Not Found
              </h2>
              <p className="text-red-600 mb-4">
                We couldn't find the blog post you're looking for. It may have
                been removed or the URL might be incorrect.
              </p>
            </div>

            {/* Suggest other blog posts */}
            <div className="mt-8">
              <h3 className="text-lg font-bold text-secondary mb-4">
                You might be interested in these posts:
              </h3>
              <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <p className="text-gray-600 mb-2">
                  Please visit our{" "}
                  <Link
                    href="/blog"
                    className="text-primary hover:underline transition-colors duration-200"
                  >
                    blog page
                  </Link>{" "}
                  to see all available posts.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
