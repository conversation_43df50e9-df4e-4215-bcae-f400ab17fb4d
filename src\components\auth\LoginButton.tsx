"use client";
import { useAuthStore } from "@/store/authStore";
import UserProfileDropdown from "./UserProfileDropdown";
import { motion } from "framer-motion";

interface LoginButtonProps {
  className?: string;
  buttonText?: string;
}

export default function LoginButton({
  buttonText = "Login / Signup",
}: LoginButtonProps) {
  const { isAuthenticated, openAuthModal } = useAuthStore();

  // If authenticated, show user profile dropdown
  if (isAuthenticated) {
    return <UserProfileDropdown />;
  }

  // If not authenticated, show login button
  return (
    <motion.button
      className="bg-secondary/2 px-6 py-2.5 text-primary rounded-lg tracking-wide border font-bold border-primary relative overflow-hidden login-btn select-none transition-colors duration-200 ease-out"
      onClick={() => openAuthModal("login-register", "general")}
      aria-label="Open login and signup modal"
      type="button"
      style={{
        userSelect: "none",
        WebkitUserSelect: "none",
        MozUserSelect: "none",
        msUserSelect: "none",
      }}
      whileHover={{
        scale: 1.02,
        backgroundColor: "var(--color-primary)",
        color: "white",
        boxShadow: "0 4px 12px rgba(145, 74, 196, 0.25)",
        transition: {
          duration: 0.2,
          ease: [0.25, 0.1, 0.25, 1.0],
        },
      }}
      whileTap={{
        scale: 0.98,
        transition: {
          duration: 0.1,
          ease: "easeOut",
        },
      }}
    >
      <span
        className="relative z-20 select-none pointer-events-none"
        style={{
          userSelect: "none",
          WebkitUserSelect: "none",
          MozUserSelect: "none",
          msUserSelect: "none",
        }}
      >
        {buttonText}
      </span>

      {/* Simplified background animation element */}
      <motion.div
        className="absolute inset-0 bg-primary rounded-lg opacity-0"
        initial={{ opacity: 0 }}
        whileHover={{
          opacity: 1,
          transition: {
            duration: 0.2,
            ease: "easeOut",
          },
        }}
        style={{
          originX: 0.5,
          originY: 0.5,
          pointerEvents: "none",
        }}
      />
    </motion.button>
  );
}
