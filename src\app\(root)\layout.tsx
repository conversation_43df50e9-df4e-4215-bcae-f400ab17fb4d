import type { <PERSON>ada<PERSON> } from "next";
import { Space_Grotesk } from "next/font/google";
import Script from "next/script";
import "../globals.css";
import Navbar from "@/components/navbar/Navbar";
import Footer from "@/components/footer/Footer";
import nunitoSansFont from "@/constants/localFont";
import Providers from "./Providers";
import CookieConsentModal from "@/components/cookie-consent/CookieConsentModal";
import ConditionalAnalytics from "@/components/analytics/ConditionalAnalytics";

const spaceGrotesk = Space_Grotesk({
  variable: "--font-space-grotesk",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"], // Reduced weights
  display: "swap",
  preload: true,
  fallback: ["system-ui", "arial"],
});

export const metadata: Metadata = {
  title: "SEO Analyser : Advanced SEO Audits, Analysis & Recommendations",
  description:
    "Get unlimited SEO audits, backlink checks & white‑label reports in one platform, clear, actionable insights to help you rank and grow. Try free today!",
  keywords: [
    "SEO Analyser",
    "SEO audit",
    "SEO analysis",
    "website audit",
    "backlink checker",
    "white label reports",
    "SEO recommendations",
    "website optimization",
    "search engine optimization",
  ],
  authors: [{ name: "SEO Analyser" }],
  creator: "SEO Analyser",
  publisher: "SEO Analyser",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_AU",
    url: "https://seoanalyser.com.au",
    siteName: "SEO Analyser",
    title: "SEO Analyser : Advanced SEO Audits, Analysis & Recommendations",
    description:
      "Get unlimited SEO audits, backlink checks & white‑label reports in one platform, clear, actionable insights to help you rank and grow. Try free today!",
    images: [
      {
        url: "https://seoanalyser.com.au/images/appLogo.svg",
        width: 1200,
        height: 630,
        alt: "SEO Analyser - Advanced SEO Audits and Analysis",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@SEO_ANALYSER",
    creator: "@SEO_ANALYSER",
    title: "SEO Analyser : Advanced SEO Audits, Analysis & Recommendations",
    description:
      "Get unlimited SEO audits, backlink checks & white‑label reports in one platform, clear, actionable insights to help you rank and grow. Try free today!",
    images: ["https://seoanalyser.com.au/images/appLogo.svg"],
  },
  alternates: {
    canonical: "https://seoanalyser.com.au",
  },
  other: {
    // Social Media Platform Links
    "linkedin:profile": "https://www.linkedin.com/in/seoanalyser-au/",
    "twitter:profile": "https://x.com/SEO_ANALYSER",
    "instagram:profile": "https://www.instagram.com/seoanalyser.com.au/",
    "facebook:profile":
      "https://www.facebook.com/profile.php?id=61573784344442",
    // Additional SEO meta tags
  },
};
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Performance optimizations */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link rel="preconnect" href="https://cdn.simpleicons.org" />
        <link rel="dns-prefetch" href="https://seoanalyser.com.au" />

        {/* Conditional Google Analytics - only loads with user consent */}
        <ConditionalAnalytics />

        {/* Hotjar Tracking Code for https://seoanalyser.com.au/ */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(h,o,t,j,a,r){
                  h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                  h._hjSettings={hjid:6462585,hjsv:6};
                  a=o.getElementsByTagName('head')[0];
                  r=o.createElement('script');r.async=1;
                  r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                  a.appendChild(r);
              })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            `,
          }}
        />

        {/* Google tag (gtag.js) */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-TZGM6QB4G1"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-TZGM6QB4G1');
          `}
        </Script>

        {/* PWA Meta Tags */}
        <meta name="application-name" content="SEO Analyser" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="SEO Analyser" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <meta name="msapplication-TileColor" content="#914ac4" />
        <meta name="msapplication-tap-highlight" content="no" />
        <meta name="theme-color" content="#914ac4" />

        {/* Apple Touch Icons */}
        <link rel="apple-touch-icon" href="/icons/icon-152x152.png" />
        <link
          rel="apple-touch-icon"
          sizes="152x152"
          href="/icons/icon-152x152.png"
        />
        <link
          rel="apple-touch-icon"
          sizes="180x180"
          href="/icons/icon-192x192.png"
        />

        {/* Favicon */}
        <link
          rel="icon"
          type="image/png"
          sizes="32x32"
          href="/icons/icon-48x48.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="16x16"
          href="/icons/icon-48x48.png"
        />
        <link rel="shortcut icon" href="/icons/icon-48x48.png" />

        {/* PWA Manifest */}
        <link rel="manifest" href="/manifest.json" />

        {/* Canonical URL */}
        <link rel="canonical" href="https://seoanalyser.com.au" />

        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              name: "SEO ANALYSER",
              url: "https://seoanalyser.com.au",
              logo: "https://seoanalyser.com.au/media/logo.png",
              contactPoint: {
                "@type": "ContactPoint",
                contactType: "Support",
                url: "https://seoanalyser.com.au/contact",
              },
              foundingDate: "2024-12-15",
            }),
          }}
        />

        {/* Website Schema */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              name: "SEO Analyser",
              url: "https://seoanalyser.com.au",
              description:
                "Advanced SEO audits, analysis & recommendations platform",
              potentialAction: {
                "@type": "SearchAction",
                target: "https://seoanalyser.com.au/?url={search_term_string}",
                "query-input": "required name=search_term_string",
              },
              publisher: {
                "@type": "Organization",
                name: "SEO Analyser",
              },
            }),
          }}
        />
      </head>
      <body
        className={`${nunitoSansFont.variable} ${spaceGrotesk.variable} font-audit antialiased relative`}
      >
        <Providers>
          <Navbar />
          {children}
          <Footer />
          <CookieConsentModal />
        </Providers>
      </body>
    </html>
  );
}
