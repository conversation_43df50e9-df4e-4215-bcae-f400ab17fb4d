"use client";
import React, { useState } from "react";
import Image from "next/image";

export interface HeaderSectionProps {
  urlName: string;
  onPageSeoData?: {
    serp_preview?: {
      url?: string;
    };
  };
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

export const HeaderSection: React.FC<HeaderSectionProps> = ({
  urlName,
  onPageSeoData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  const [failedImages, setFailedImages] = useState<Record<string, boolean>>({});

  // Get current date for report generation timestamp
  const today = new Date();
  const time = {
    day: today.getDate(),
    month: today.toLocaleString("default", { month: "long" }),
    year: today.getFullYear(),
    hours: today.getHours(),
    minutes: today.getMinutes().toString().padStart(2, "0"),
  };

  const handleImageError = (id: string) => {
    setFailedImages((prev) => ({
      ...prev,
      [id]: true,
    }));
    onImageError?.(id);
  };

  const handleImageLoad = (id: string) => {
    onImageLoad?.(id);
  };

  return (
    <div className="relative">
      {/* Professional header with modern design and larger logo */}
      <div className="bg-gradient-to-br from-primary/10 via-primary/6 to-primary/4 rounded-3xl p-8 border border-primary/25">
        <div className="flex justify-between items-start gap-8">
          <div className="flex items-center gap-8">
            {/* Enhanced brand logo - extra large */}
            <div className="w-32 h-32 relative flex-shrink-0 flex items-center justify-center pdf-header-logo">
              {brand_photo != null || true ? (
                <div className="w-full h-full relative flex justify-center items-center">
                  {/* Try regular img tag first for better PDF compatibility */}
                  <img
                    src={brand_photo || "/images/appLogo.svg"}
                    alt="Brand Logo"
                    className="w-full h-full object-contain "
                    referrerPolicy="no-referrer"
                    onLoad={() => handleImageLoad("brandLogo")}
                    onError={() => handleImageError("brandLogo")}
                    style={{
                      maxWidth: "128px",
                      maxHeight: "158px",
                      width: "128px",
                      height: "158px",
                    }}
                  />
                  {failedImages["brandLogo"] && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
                      <span className="text-gray-600 font-bold text-lg">
                        LOGO
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded-lg">
                  <span className="text-gray-600 font-bold text-lg">LOGO</span>
                </div>
              )}
            </div>

            <div className="space-y-3">
              <h1 className="text-4xl font-bold text-primary tracking-tight leading-tight">
                SEO Audit Report
              </h1>
              <div className="flex items-center gap-4 text-base">
                <p className="text-gray-700 font-medium">
                  Generated on {time.day} {time.month} {time.year} at{" "}
                  {time.hours}:{time.minutes}
                </p>
                <>
                  <span className="w-1.5 h-1.5 bg-gray-400 rounded-full"></span>
                  <p
                    className="text-primary font-semibold"
                    style={{ textTransform: "none", fontVariant: "normal" }}
                  >
                    Audited by:{" "}
                    {brand_website ? (
                      <a
                        href={
                          brand_website.startsWith("http")
                            ? brand_website
                            : `https://${brand_website}`
                        }
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:underline cursor-pointer"
                      >
                        {
                          brand_website
                            .replace(/^https?:\/\//, "") // Remove http:// or https://
                            .replace(/^www\./, "") // Remove www.
                        }
                      </a>
                    ) : (
                      "SEO ANALYSER"
                    )}
                  </p>
                </>
              </div>
            </div>
          </div>

          {/* Website info card - enhanced design */}
          <div className="text-center bg-white/80 backdrop-blur-sm rounded-2xl p-4 border-2 border-white/60 min-w-0 flex-shrink">
            <a
              href={
                (onPageSeoData?.serp_preview?.url || urlName).startsWith("http")
                  ? onPageSeoData?.serp_preview?.url || urlName
                  : `https://${onPageSeoData?.serp_preview?.url || urlName}`
              }
              target="_blank"
              rel="noopener noreferrer"
              className="hover:underline cursor-pointer"
            >
              <h2 className="text-lg   font-bold text-primary mb-2 break-all leading-tight">
                {
                  (onPageSeoData?.serp_preview?.url || urlName)
                    .replace(/^https?:\/\//, "") // Remove http:// or https://
                    .replace(/^www\./, "") // Remove www.
                }
              </h2>
            </a>
            {brand_website ? (
              <a
                href={
                  brand_website.startsWith("http")
                    ? brand_website
                    : `https://${brand_website}`
                }
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline cursor-pointer"
              >
                <span
                  className="text-xs lg:text-base font-medium text-primary/80 bg-primary/10 px-3 py-1 rounded-lg"
                  style={{ textTransform: "none", fontVariant: "normal" }}
                >
                  {
                    brand_website
                      .replace(/^https?:\/\//, "") // Remove http:// or https://
                      .replace(/^www\./, "") // Remove www.
                  }
                </span>
              </a>
            ) : (
              <span
                className="text-xs lg:text-base font-medium text-primary/80 bg-primary/10 px-3 py-1 rounded-lg"
                style={{ textTransform: "none", fontVariant: "normal" }}
              >
                SEO ANALYSER
              </span>
            )}
          </div>
        </div>

        {/* Professional description - enhanced styling */}
        <div className="mt-6 bg-white/70 backdrop-blur-sm p-6 rounded-2xl border-2 border-white/60">
          <p className="text-gray-700 text-base leading-relaxed">
            This comprehensive SEO audit provides an in-depth assessment of{" "}
            <a
              href={
                (onPageSeoData?.serp_preview?.url || urlName).startsWith("http")
                  ? onPageSeoData?.serp_preview?.url || urlName
                  : `https://${onPageSeoData?.serp_preview?.url || urlName}`
              }
              target="_blank"
              rel="noopener noreferrer"
              className="hover:underline cursor-pointer"
            >
              <span className="font-semibold text-primary bg-primary/10 px-2 py-0.5 rounded mx-1">
                {
                  (onPageSeoData?.serp_preview?.url || urlName)
                    .replace(/^https?:\/\//, "") // Remove http:// or https://
                    .replace(/^www\./, "") // Remove www.
                }
              </span>
            </a>
            &apos;s performance across key SEO factors, including on-page
            optimisation, technical infrastructure, site usability, page-speed
            performance and search-engine visibility. Each section offers
            data-driven insights and actionable recommendations to boost your
            website&apos;s search rankings, enhance user experience and
            strengthen its overall online presence.
          </p>
        </div>
      </div>
    </div>
  );
};
