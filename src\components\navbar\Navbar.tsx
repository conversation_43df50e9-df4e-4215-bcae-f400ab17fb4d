"use client";
import { usePathname } from "next/navigation";
import NavButtons from "./NavButtons";
import NavLogo from "./NavLogo";
import { MenuHamburgerIcon } from "@/ui/icons/general";
import SidebarMenu from "./SidebarMenu";
import { useEffect, useState } from "react";
import DesktopMenu from "./DesktopMenu";

export default function Navbar() {
  const pathname = usePathname();
  const isHome = pathname === "/";

  const menuItems = [
    {
      label: "Home",
      href: "/",
    },
    // {
    //   label: "SEO Resources",
    //   href: "#",
    //   isDropdown: true,
    //   subMenus: [
    //     {
    //       label: "About Us",
    //       href: "/aboutUs",
    //       description:
    //         "Get to know our mission, values, and expertise in the world of SEO. We are committed to helping businesses improve their online visibility and achieve top rankings on search engines.",
    //     },
    //     {
    //       label: "Contact Us",
    //       href: "/contactUs",
    //       description:
    //         "Have questions? Need expert SEO advice? Our team is here to help! Reach out to us for personalized solutions and consultations.",
    //     },
    //     {
    //       label: "Blog",
    //       href: "#",
    //       description:
    //         "Stay ahead in the SEO game with our expert articles, latest industry updates, and in-depth guides on search engine optimization.",
    //     },
    //   ],
    // },
    {
      label: "Blog",
      href: "/blog",
    },
    { label: "Go Pro", href: "/pricing", isSpecial: true },
    {
      label: "Contact Us",
      href: "/contactUs",
    },
    {
      label: "About Us",
      href: "/aboutUs",
    },
    // {
    //   label: "Privacy Policy",
    //   href: "/privacy-policy",
    // },
    // {
    //   label: "Powerful Features",
    //   href: "#",
    //   isDropdown: true,
    //   subMenus: [
    //     {
    //       label: "Comprehensive SEO Audit",
    //       href: "#",
    //       description:"Our tool analyzes all Technology and content aspects of your website, identifying SEO issues and providing optimization suggestions."
    //     },
    //     {
    //       label: "On-Page & Off-Page SEO Analysis",
    //       href: "#",
    //       description:"Detailed review of titles, meta descriptions, headings, and content optimization Evaluation of backlink quality and strategic link-building recommendations"
    //     },
    //     {
    //       label: "Rank Tracking & Performance Monitoring",
    //       href: "#",
    //       description:"Track keyword rankings across search engines Compare your website’s performance with competitors"
    //     },
    //     {
    //       label: "Content Optimization & Keyword Suggestions",
    //       href: "#",
    //       description:"Analyze content quality and suggest high-performing keywords Provide structured guidelines for better readability and ranking"
    //     },
    //     {
    //       label: "Detailed & Professional Reports",
    //       href: "#",
    //       description:"Downloadable reports on your site’s SEO performance Step-by-step recommendations for improving your website"
    //     },
    //   ],
    // },
  ];

  const [openSide, setOpenSide] = useState(false);

  useEffect(() => {
    if (isHome) {
      document.body.classList.add("overflow-x-hidden");
      document.documentElement.classList.add("overflow-x-hidden");
    } else {
      document.body.classList.remove("overflow-x-hidden");
      document.documentElement.classList.remove("overflow-x-hidden");
    }
  }, [pathname, isHome]);

  return (
    <header className="no-print pt-4 lg:pt-10 w-full container max-w-full__customeLG mx-auto relative">
      {isHome && (
        <>
          <CircleTR />
          <CircleTL />
        </>
      )}

      <SidebarMenu
        open={openSide}
        setOpen={setOpenSide}
        menuItems={menuItems}
      />
      <nav
        className="w-full flex lg:grid grid-cols-3 items-center justify-between lg:justify-baseline relative"
        role="navigation"
        aria-label="Main navigation"
      >
        <div className="hidden lg:block">
          <DesktopMenu menuItems={menuItems} />
        </div>
        <div className="flex justify-center xl:pl-0">
          <NavLogo />
        </div>
        <div className="lg:hidden relative z-10">
          <button
            onClick={() => setOpenSide(true)}
            aria-label="Open navigation menu"
            aria-expanded={openSide}
            aria-controls="mobile-navigation"
            type="button"
          >
            <MenuHamburgerIcon className="w-8 h-8 text-secondary" />
          </button>
        </div>
        <div className="hidden  h-full lg:flex  items-center justify-end">
          <NavButtons />
        </div>
      </nav>
      {isHome && (
        <>
          <Shape />
          <ShapeTr />
        </>
      )}
    </header>
  );
}

function Shape() {
  return (
    <svg
      width="49"
      height="52"
      viewBox="0 0 49 52"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-[35px] h-[38px] lg:w-[49px] lg:h-[52px] absolute bottom-[-135px] left-[18px] lg:bottom-[-94px] lg:left-[34px] floating-animate-reverse"
    >
      <g filter="url(#filter0_f_58_51)">
        <path
          d="M21.3 11.8475C23.2802 10.7043 25.7198 10.7043 27.7 11.8475L35.1564 16.1525C37.1366 17.2957 38.3564 19.4085 38.3564 21.695V30.305C38.3564 32.5915 37.1366 34.7043 35.1564 35.8475L27.7 40.1525C25.7198 41.2957 23.2802 41.2957 21.3 40.1525L13.8436 35.8475C11.8634 34.7043 10.6436 32.5915 10.6436 30.305L10.6436 21.695C10.6436 19.4085 11.8634 17.2957 13.8436 16.1525L21.3 11.8475Z"
          fill="#EACCFF"
        />
      </g>
      <defs>
        <filter
          id="filter0_f_58_51"
          x="0.0436001"
          y="0.390081"
          width="48.9128"
          height="51.2198"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="5.3"
            result="effect1_foregroundBlur_58_51"
          />
        </filter>
      </defs>
    </svg>
  );
}

function ShapeTr() {
  return (
    <svg
      width="49"
      height="52"
      viewBox="0 0 49 52"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-[35px] h-[38px] lg:w-[49px] lg:h-[52px] absolute -bottom-6 lg:bottom-[-94px] right-5 lg:right-[-35px] z-20 floating-animate"
    >
      <g filter="url(#filter0_f_58_50)">
        <path
          d="M21.3 11.8475C23.2802 10.7043 25.7198 10.7043 27.7 11.8475L35.1564 16.1525C37.1366 17.2957 38.3564 19.4085 38.3564 21.695V30.305C38.3564 32.5915 37.1366 34.7043 35.1564 35.8475L27.7 40.1525C25.7198 41.2957 23.2802 41.2957 21.3 40.1525L13.8436 35.8475C11.8634 34.7043 10.6436 32.5915 10.6436 30.305L10.6436 21.695C10.6436 19.4085 11.8634 17.2957 13.8436 16.1525L21.3 11.8475Z"
          fill="#914AC4"
        />
      </g>
      <defs>
        <filter
          id="filter0_f_58_50"
          x="0.0435543"
          y="0.390081"
          width="48.9129"
          height="51.2198"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="5.3"
            result="effect1_foregroundBlur_58_50"
          />
        </filter>
      </defs>
    </svg>
  );
}

function CircleTL() {
  return (
    <svg
      width="359"
      height="960"
      viewBox="0 0 359 960"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-[649px] h-[649px] absolute top-[111px] -left-[100px] lg:block hidden"
    >
      <g filter="url(#filter0_f_720_377)">
        <circle
          cx="-165.5"
          cy="435.5"
          r="324.5"
          fill="#914AC4"
          fillOpacity="0.1"
        />
      </g>
      <defs>
        <filter
          id="filter0_f_720_377"
          x="-690"
          y="-89"
          width="1049"
          height="1049"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="100"
            result="effect1_foregroundBlur_720_377"
          />
        </filter>
      </defs>
    </svg>
  );
}

function CircleTR() {
  return (
    <svg
      width="642"
      height="762"
      viewBox="0 0 642 762"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-[735px] h-[735px] absolute -top-[200px] lg:-top-[140px] -right-[150px]"
    >
      <g filter="url(#filter0_f_720_378)">
        <circle
          cx="594.5"
          cy="167.5"
          r="367.5"
          fill="#914AC4"
          fillOpacity="0.1"
        />
      </g>
      <defs>
        <filter
          id="filter0_f_720_378"
          x="0.497696"
          y="-426.502"
          width="1188"
          height="1188"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="113.251"
            result="effect1_foregroundBlur_720_378"
          />
        </filter>
      </defs>
    </svg>
  );
}
