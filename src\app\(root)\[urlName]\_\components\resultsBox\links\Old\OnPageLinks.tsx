import Table from "@/ui/Table";

type OnPageLinksProps = {
  totalLinks: number;
  externalLinks: number;
  nofollowLinks: number;
  externalPercentage: number;
  nofollowPercentage: number;
};

export default function OnPageLinks({
  totalLinks,
  externalLinks,
  nofollowLinks,
  externalPercentage,
  nofollowPercentage,
}: OnPageLinksProps) {
  const tableData = [
    {
      metric: "Total Links",
      value: totalLinks.toString(),
    },
    {
      metric: "External Links",
      value: externalLinks.toString(),
    },
    {
      metric: "Nofollow Links",
      value: nofollowLinks.toString(),
    },
    {
      metric: "External Links Percentage",
      value: `${externalPercentage}%`,
    },
    {
      metric: "Nofollow Links Percentage",
      value: `${nofollowPercentage}%`,
    },
  ];

  return (
    <div className="mt-4 lg:mt-6">
      <div className="mb-6">
        <h4 className="font-semibold text-secondary pb-2">On-Page Links Summary</h4>
        <span className="text-sm text-secondary/60">
          Analysis of the links found on your page, including internal, external, and nofollow links.
        </span>
      </div>

      <Table>
        <Table.Header>
          <th className="pb-4">Metric</th>
          <th className="text-center pb-4">Value</th>
        </Table.Header>
        <Table.Body>
          {tableData.map((item, index) => (
            <Table.Row key={index}>
              <td className="text-xs sm:text-sm">{item.metric}</td>
              <td className="text-center text-xs sm:text-sm">{item.value}</td>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </div>
  );
}
