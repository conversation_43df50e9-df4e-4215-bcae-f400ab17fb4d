import ProgressChart from "@/ui/charts/ProgressChart";
import BoxPrimary from "../../BoxPrimary";
import Image from "next/image";
import GooglePageSpeed from "./GooglePageSpeed";
import ShowMoreSection from "./ShowMoreSection";
import { DocumentCheckIcon, DocumentCrossIcon } from "@/ui/icons/general";
import {
  UsabilityAnalysis,
  PageSpeedAnalysis,
  PageSpeedMobileAnalysis,
} from "@/types/seoAnalyzerTypes";
import { memo, useState } from "react";
import LoadingSlate from "@/components/loading/LoadingSlate";
import OverallSection from "../OverallSection";

// No need for type aliases as we're using the imported types directly

// Define types for PageSpeed data that include the imported types
type PageSpeedDesktopData = PageSpeedAnalysis;
type PageSpeedMobileData = PageSpeedMobileAnalysis;

type UsabilityProps = {
  results:
    | (UsabilityAnalysis & {
        pagespeed_analysis?: PageSpeedDesktopData;
        pagespeed_mobile_analysis?: PageSpeedMobileData;
      })
    | Record<string, any>
    | string
    | null;
};

// Memoized Mobile Screenshot component to prevent unnecessary re-renders
const MobileScreenshot = memo(
  ({ src, shouldLoad }: { src: string; shouldLoad: boolean }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);

    return (
      <div className="w-full h-full flex items-center justify-center bg-white relative">
        {(!shouldLoad || isLoading) && !hasError && (
          <div className="w-full h-full bg-gray-100 animate-pulse flex items-center justify-center">
            {!shouldLoad && (
              <div className="text-center p-4">
                <div className="w-6 h-6 mx-auto mb-2 bg-gray-300 rounded flex items-center justify-center">
                  <svg
                    className="w-3 h-3 text-gray-500 animate-spin"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                </div>
                <p className="text-xs text-gray-500">Loading...</p>
              </div>
            )}
          </div>
        )}

        {shouldLoad && (
          <Image
            src={src || ""}
            alt="Mobile screenshot"
            width={375}
            height={667}
            className={`w-full h-full object-contain ${
              isLoading && !hasError ? "opacity-0" : "opacity-100"
            }`}
            onLoad={() => setIsLoading(false)}
            onError={() => {
              setIsLoading(false);
              setHasError(true);
            }}
            unoptimized // Prevents Next.js from optimizing the image, which can cause re-fetching
          />
        )}

        {hasError && shouldLoad && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center p-4">
              <div className="w-8 h-8 mx-auto mb-2 bg-gray-300 rounded flex items-center justify-center">
                <svg
                  className="w-4 h-4 text-gray-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <p className="text-xs text-gray-500">Image failed to load</p>
            </div>
          </div>
        )}
      </div>
    );
  }
);
MobileScreenshot.displayName = "MobileScreenshot";

// Memoized Tablet Screenshot component with pure CSS frame
const TabletScreenshot = memo(
  ({ src, shouldLoad }: { src: string; shouldLoad: boolean }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);

    return (
      <div className="relative max-w-[380px] w-full h-auto aspect-[380/494] mx-auto">
        {/* Pure CSS iPad-style Tablet Frame */}
        <div className="w-full h-full bg-black rounded-[24px] p-[12px]">
          {/* Screen area */}
          <div className="w-full h-full bg-white rounded-[16px] overflow-hidden relative">
            {/* Home button indicator */}
            <div className="absolute bottom-[6px] left-1/2 transform -translate-x-1/2 w-[40px] h-[4px] bg-gray-800 rounded-full"></div>

            {/* Screenshot content */}
            <div className="w-full h-full flex items-center justify-center bg-white relative">
              {(!shouldLoad || isLoading) && !hasError && (
                <div className="w-full h-full bg-gray-100 animate-pulse flex items-center justify-center">
                  {!shouldLoad && (
                    <div className="text-center p-4">
                      <div className="w-8 h-8 mx-auto mb-2 bg-gray-300 rounded flex items-center justify-center">
                        <svg
                          className="w-4 h-4 text-gray-500 animate-spin"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                      </div>
                      <p className="text-xs text-gray-500">Loading...</p>
                    </div>
                  )}
                </div>
              )}

              {shouldLoad && (
                <Image
                  src={src || ""}
                  alt="Tablet screenshot"
                  width={1000}
                  height={1000}
                  className={`w-full h-full object-cover ${
                    isLoading && !hasError ? "opacity-0" : "opacity-100"
                  }`}
                  onLoad={() => setIsLoading(false)}
                  onError={() => {
                    setIsLoading(false);
                    setHasError(true);
                  }}
                  unoptimized
                />
              )}

              {hasError && shouldLoad && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                  <div className="text-center p-4">
                    <div className="w-12 h-12 mx-auto mb-2 bg-gray-300 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-6 h-6 text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <p className="text-xs text-gray-500">
                      Image failed to load
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }
);
TabletScreenshot.displayName = "TabletScreenshot";

function Usability({ results }: UsabilityProps) {
  // Optimized data handling - show content immediately when we have ANY meaningful data
  // This allows us to display partial results as they become available
  const hasAnyData =
    results &&
    typeof results === "object" &&
    results !== null &&
    (results.total_score ||
      results.device_rendering ||
      results.viewport_usage ||
      results.flash_usage ||
      results.iframes_usage ||
      results.favicon_presence ||
      results.overall_title ||
      Object.keys(results).length > 0);

  // Only show loading state if we have absolutely no data
  if (!hasAnyData) {
    return (
      <BoxPrimary title="Usability">
        <LoadingSlate
          title="Loading usability results..."
          showHeader={true}
          showCards={true}
          cardCount={4}
          showChart={true}
          showProgress={false}
          height="lg"
        />
      </BoxPrimary>
    );
  }

  // Check if we have the device_rendering section which is expected in the API response
  const hasDeviceRendering =
    results.device_rendering &&
    typeof results.device_rendering === "object" &&
    "description" in results.device_rendering;

  // Check if screenshots should be loaded (only when analysis is complete)
  const shouldLoadScreenshots =
    hasDeviceRendering &&
    results.device_rendering?.screenshot_urls &&
    (results.total_score || // Has final score
      (typeof results === "object" && Object.keys(results).length > 5)); // Has substantial data

  // Check if we have the viewport_usage section which is expected in the API response
  const hasViewportUsage =
    results.viewport_usage &&
    typeof results.viewport_usage === "object" &&
    "description" in results.viewport_usage;

  // Safe access to nested properties with fallbacks
  const overallTitle = results.overall_title || "Usability Analysis";
  const overallDescription =
    results.overall_description ||
    "Analyzing your website's usability and mobile-friendliness...";

  // Get the total score if available
  const totalScore =
    results.total_score && results.total_score.grade
      ? (results.total_score.grade as
          | "A+"
          | "A"
          | "A-"
          | "B+"
          | "B"
          | "B-"
          | "C+"
          | "C"
          | "C-"
          | "D+"
          | "D"
          | "D-"
          | "F")
      : ("B" as const);

  // Get the numeric score if available
  const numericScore =
    results.total_score && results.total_score.score
      ? results.total_score.score
      : undefined;

  return (
    <BoxPrimary title="Usability">
      <div className="w-full flex flex-col lg:flex-row items-center lg:items-start gap-6">
        <ProgressChart
          value={totalScore}
          title="Usability Score"
          size="lg"
          progressStates={[
            {
              label: "Grade",
              value: numericScore ? numericScore : 0,
              isNoColor: false,
            },
          ]}
        />

        <OverallSection title={overallTitle} description={overallDescription} />
      </div>
      {/* Device Rendering Section */}
      {hasDeviceRendering ? (
        <div className="my-4 lg:my-6">
          <div className="mb-4">
            <h4 className="font-semibold text-secondary pb-4">
              Device Rendering
            </h4>
            <span className="text-sm text-secondary/60">
              {results.device_rendering?.description}
            </span>
          </div>

          <div className="w-full flex flex-col lg:flex-row items-center justify-center gap-4 overflow-x-hidden">
            {/* Mobile Screenshot */}
            {(shouldLoadScreenshots ||
              results.device_rendering?.screenshot_urls?.mobile) && (
              <div className="relative max-w-[220px] w-full h-auto aspect-[220/453] rounded-[32px] bg-black p-[10px] shadow-lg mx-auto">
                {/* Phone top notch */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[80px] h-[25px] bg-black rounded-b-[14px] z-10 flex items-center justify-center">
                  <div className="w-[8px] h-[8px] rounded-full bg-gray-600 mx-1"></div>
                </div>
                {/* Phone screen */}
                <div className="w-full h-full rounded-[24px] overflow-hidden border-[2px] border-gray-700 relative">
                  <MobileScreenshot
                    src={
                      results.device_rendering?.screenshot_urls?.mobile || ""
                    }
                    shouldLoad={shouldLoadScreenshots}
                  />
                </div>
                {/* Phone home button */}
                <div className="absolute bottom-[5px] left-1/2 transform -translate-x-1/2 w-[40px] h-[5px] bg-gray-600 rounded-full"></div>
              </div>
            )}

            {/* Tablet Screenshot with Pure CSS Frame */}
            {(shouldLoadScreenshots ||
              results.device_rendering?.screenshot_urls?.tablet) && (
              <TabletScreenshot
                src={results.device_rendering?.screenshot_urls?.tablet || ""}
                shouldLoad={shouldLoadScreenshots}
              />
            )}
          </div>
        </div>
      ) : (
        <div className="my-4 lg:my-6 animate-pulse">
          <div className="mb-4">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
          <div className="w-full flex flex-col lg:flex-row items-center justify-center gap-4">
            <div className="relative max-w-[220px] w-full h-auto aspect-[220/453] rounded-[32px] bg-gray-200 mx-auto"></div>
            <div className="relative max-w-[380px] w-full h-auto aspect-[380/494] bg-gray-200 rounded mx-auto"></div>
          </div>
        </div>
      )}
      {/* Viewport Usage Section */}
      {hasViewportUsage && (
        <div className="my-8">
          <ShowMoreSection
            title="Use of Mobile Viewports"
            passed={results.viewport_usage?.pass || false}
            description={results.viewport_usage?.description || ""}
            icon={
              results.viewport_usage?.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.viewport_usage?.importance || ""}
            recommendation={results.viewport_usage?.recommendation}
          >
            <div className="text-sm text-secondary/80">
              <p className="mb-2">
                <strong>Has Viewport Meta Tag:</strong>{" "}
                {results.viewport_usage?.has_viewport_meta ? "Yes" : "No"}
              </p>
              {results.viewport_usage?.viewport_content && (
                <p className="mb-2">
                  <strong>Viewport Content:</strong>{" "}
                  {results.viewport_usage.viewport_content}
                </p>
              )}
              <p className="mb-2">
                <strong>Is Responsive:</strong>{" "}
                {results.viewport_usage?.is_responsive ? "Yes" : "No"}
              </p>
            </div>
          </ShowMoreSection>
        </div>
      )}
      {/* Google PageSpeed section */}

      <GooglePageSpeed
        pageSpeedDesktop={results.pagespeed_analysis}
        pageSpeedMobile={results.pagespeed_mobile_analysis}
      />
      {/* <UsabilityChart
        deviceRendering={results.device_rendering?.score || 0}
        viewportUsage={results.viewport_usage?.score || 0}
        flashUsage={results.flash_usage?.score || 0}
        iframesUsage={results.iframes_usage?.score || 0}
        iframeProtection={results.iframe_protection?.score || 0}
        faviconPresence={results.favicon_presence?.score || 0}
        emailPrivacy={results.email_privacy?.score || 0}
        fontLegibility={results.font_legibility?.score || 0}
        tapTargetSizing={results.tap_target_sizing?.score || 0}
      /> */}
      <div className="flex flex-col gap-8 mt-4 lg:mt-6">
        {/* Flash Usage Section */}
        {results.flash_usage && (
          <ShowMoreSection
            title={
              results.flash_usage?.pass ? "No Flash Found" : "Flash Found!"
            }
            passed={results.flash_usage?.pass}
            description={results.flash_usage.description}
            icon={
              results.flash_usage.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.flash_usage?.importance || "Medium"}
            recommendation={
              results.flash_usage?.recommendation || {
                text: "Remove Flash elements",
                priority: "High",
              }
            }
          >
            <div className="text-sm text-secondary/80">
              <p className="mb-2">
                <strong>Flash Elements Found:</strong>{" "}
                {results.flash_usage?.count || 0}
              </p>
              {results.flash_usage?.elements &&
                results.flash_usage.elements.length > 0 && (
                  <div className="mt-2">
                    <p className="mb-1">
                      <strong>Flash Elements:</strong>
                    </p>
                    <ul className="list-disc pl-5">
                      {results.flash_usage.elements.map(
                        (element: any, index: number) => (
                          <li key={index} className="text-secondary/80">
                            {element}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}

        {/* iFrames Usage Section */}
        {results.iframes_usage && (
          <ShowMoreSection
            title={
              results.iframes_usage.pass ? "iFrame not found" : "iFrame found!"
            }
            passed={results.iframes_usage.pass} // Inverted because status=true means iframes are used, which might be concerning
            description={results.iframes_usage.description}
            icon={
              results.iframes_usage.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.iframes_usage.importance || ""}
            recommendation={results.iframes_usage.recommendation}
          >
            <div className="text-sm text-secondary/80">
              <p className="mb-2">
                <strong>iFrames Found:</strong> {results.iframes_usage.count}
              </p>
              {results.iframes_usage.iframe_sources &&
                results.iframes_usage.iframe_sources.length > 0 && (
                  <div className="mt-2">
                    <p className="mb-1">
                      <strong>iFrame Sources:</strong>
                    </p>
                    <ul className="list-disc pl-5">
                      {results.iframes_usage.iframe_sources.map(
                        (source: string, index: number) => (
                          <li
                            key={index}
                            className="text-secondary/80 break-all"
                          >
                            {source}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}

        {/* iFrame Protection Section */}
        {results.iframe_protection && (
          <ShowMoreSection
            title="iFrames Protection"
            passed={results.iframe_protection.pass}
            description={results.iframe_protection.description}
            icon={
              results.iframe_protection.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.iframe_protection.importance || ""}
            recommendation={results.iframe_protection.recommendation}
          >
            <div className="text-sm text-secondary/80">
              <p className="mb-2">
                <strong>X-Frame-Options Header:</strong>{" "}
                {results.iframe_protection.has_x_frame_options
                  ? "Present"
                  : "Not Present"}
              </p>
              {results.iframe_protection.has_x_frame_options && (
                <p className="mb-2">
                  <strong>X-Frame-Options Value:</strong>{" "}
                  {results.iframe_protection.x_frame_options_value ||
                    "Not specified"}
                </p>
              )}
              <p className="mb-2">
                <strong>CSP Frame Protection:</strong>{" "}
                {results.iframe_protection.has_csp_frame_protection
                  ? "Present"
                  : "Not Present"}
              </p>
            </div>
          </ShowMoreSection>
        )}

        {/* Favicon Section */}
        {results.favicon_presence && (
          <ShowMoreSection
            title="Favicon"
            passed={results.favicon_presence.pass}
            description={results.favicon_presence.description}
            icon={
              results.favicon_presence.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.favicon_presence.importance || ""}
            recommendation={results.favicon_presence.recommendation}
          >
            <div className="text-sm text-secondary/80">
              <p className="mb-2">
                <strong>Favicon Present:</strong>{" "}
                {results.favicon_presence.has_favicon ? "Yes" : "No"}
              </p>
              <p className="mb-2">
                <strong>Apple Touch Icon Present:</strong>{" "}
                {results.favicon_presence.has_apple_touch_icon ? "Yes" : "No"}
              </p>

              {results.favicon_presence.favicon_paths &&
                results.favicon_presence.favicon_paths.length > 0 && (
                  <div className="mt-3">
                    <p className="mb-1">
                      <strong>Favicon Paths:</strong>
                    </p>
                    <ul className="list-disc pl-5">
                      {results.favicon_presence.favicon_paths.map(
                        (path: string, index: number) => (
                          <li
                            key={index}
                            className="text-secondary/80 break-all"
                          >
                            {path}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}

              {results.favicon_presence.apple_touch_icon_paths &&
                results.favicon_presence.apple_touch_icon_paths.length > 0 && (
                  <div className="mt-3">
                    <p className="mb-1">
                      <strong>Apple Touch Icon Paths:</strong>
                    </p>
                    <ul className="list-disc pl-5">
                      {results.favicon_presence.apple_touch_icon_paths.map(
                        (path: string, index: number) => (
                          <li
                            key={index}
                            className="text-secondary/80 break-all"
                          >
                            {path}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}

        {/* Email Privacy Section */}
        {results.email_privacy && (
          <ShowMoreSection
            title="Email Privacy"
            passed={results.email_privacy.pass}
            description={results.email_privacy.description}
            icon={
              results.email_privacy.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.email_privacy.importance || ""}
            recommendation={results.email_privacy.recommendation}
          >
            <div className="text-sm text-secondary/80">
              <p className="mb-2">
                <strong>Exposed Email Count:</strong>{" "}
                {results.email_privacy.exposed_email_count}
              </p>

              {results.email_privacy.exposed_emails_sample &&
                results.email_privacy.exposed_emails_sample.length > 0 && (
                  <div className="mt-3">
                    <p className="mb-1">
                      <strong>Exposed Emails Sample:</strong>
                    </p>
                    <ul className="list-disc pl-5">
                      {results.email_privacy.exposed_emails_sample.map(
                        (email: string, index: number) => (
                          <li
                            key={index}
                            className="text-secondary/80 break-all"
                          >
                            {email}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}

        {/* Font Legibility Section */}
        {results.font_legibility && (
          <ShowMoreSection
            title="Legible Font Sizes"
            passed={results.font_legibility.pass}
            description={results.font_legibility.description}
            icon={
              results.font_legibility.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.font_legibility.importance || ""}
            recommendation={results.font_legibility.recommendation}
          >
            <div className="text-sm text-secondary/80">
              <p className="mb-2">
                <strong>Small Font Issues:</strong>{" "}
                {results.font_legibility.small_font_issue_count}
              </p>
              <p className="mb-2">
                <strong>Element Analysed (Inline):</strong>{" "}
                {results.font_legibility.elements_analyzed_inline}
              </p>
              <p className="mb-2">
                <strong>CSS Declarations Analysed:</strong>{" "}
                {results.font_legibility.declarations_analyzed_css}
              </p>
              <p className="mb-2">
                <strong>External CSS:</strong>{" "}
                {results.font_legibility.has_external_css
                  ? "Present"
                  : "Not Present"}
              </p>
              <p className="mb-2">
                <strong>External CSS Links:</strong>{" "}
                {results.font_legibility.external_css_total_links}
              </p>

              {results.font_legibility.css_issues &&
                results.font_legibility.css_issues.length > 0 && (
                  <div className="mt-3">
                    <p className="mb-1">
                      <strong>CSS Issues:</strong>
                    </p>
                    <ul className="list-disc pl-5">
                      {results.font_legibility.css_issues.map(
                        (issue: any, index: number) => (
                          <li key={index} className="text-secondary/80 mb-2">
                            <strong>Selector:</strong> {issue.selector}
                            <br />
                            <strong>Size:</strong> {issue.size}
                            <br />
                            <strong>Severity:</strong> {issue.severity}
                            <br />
                            <strong>Source:</strong> {issue.source}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}

              {results.font_legibility.timing && (
                <div className="mt-3">
                  <p className="mb-1">
                    <strong>Analysis Timing:</strong>
                  </p>
                  <ul className="list-none pl-0">
                    <li className="text-secondary/80 mb-1">
                      <strong>Total Analysis Time:</strong>{" "}
                      {results.font_legibility.timing.total_analysis_time.toFixed(
                        2
                      )}
                      s
                    </li>
                    <li className="text-secondary/80 mb-1">
                      <strong>Inline Styles Time:</strong>{" "}
                      {results.font_legibility.timing.inline_styles_time.toFixed(
                        2
                      )}
                      s
                    </li>
                    <li className="text-secondary/80 mb-1">
                      <strong>Internal CSS Time:</strong>{" "}
                      {results.font_legibility.timing.internal_css_time.toFixed(
                        2
                      )}
                      s
                    </li>
                    <li className="text-secondary/80 mb-1">
                      <strong>External CSS Time:</strong>{" "}
                      {results.font_legibility.timing.external_css_time.toFixed(
                        2
                      )}
                      s
                    </li>
                  </ul>
                </div>
              )}
            </div>
          </ShowMoreSection>
        )}

        {/* Tap Target Sizing Section */}
        {results.tap_target_sizing && (
          <ShowMoreSection
            title="Tap Target Sizing"
            passed={results.tap_target_sizing.pass}
            description={results.tap_target_sizing.description}
            icon={
              results.tap_target_sizing.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.tap_target_sizing.importance || ""}
            recommendation={results.tap_target_sizing.recommendation}
          >
            <div className="text-sm text-secondary/80">
              <p className="mb-2">
                <strong>Total Interactive Elements:</strong>{" "}
                {results.tap_target_sizing.total_interactive_elements}
              </p>
              <p className="mb-2">
                <strong>Problematic Elements:</strong>{" "}
                {results.tap_target_sizing.problematic_elements_count}
              </p>
              <p className="mb-2">
                <strong>Has Touch-Specific CSS:</strong>{" "}
                {results.tap_target_sizing.has_touch_specific_css
                  ? "Yes"
                  : "No"}
              </p>

              {results.tap_target_sizing.touch_media_queries &&
                results.tap_target_sizing.touch_media_queries.length > 0 && (
                  <div className="mt-3">
                    <p className="mb-1">
                      <strong>Touch Media Queries:</strong>
                    </p>
                    <ul className="list-disc pl-5">
                      {results.tap_target_sizing.touch_media_queries.map(
                        (query: string, index: number) => (
                          <li key={index} className="text-secondary/80">
                            {query}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}

              {results.tap_target_sizing.problematic_elements &&
                results.tap_target_sizing.problematic_elements.length > 0 && (
                  <div className="mt-3">
                    <p className="mb-1">
                      <strong>Problematic Elements:</strong>
                    </p>
                    <ul className="list-disc pl-5">
                      {results.tap_target_sizing.problematic_elements.map(
                        (element: any, index: number) => (
                          <li key={index} className="text-secondary/80 mb-2">
                            {JSON.stringify(element)}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}
      </div>
    </BoxPrimary>
  );
}

// Export the memoized component to prevent unnecessary re-renders
const MemoizedUsability = memo(Usability);
MemoizedUsability.displayName = "Usability";
export default MemoizedUsability;
