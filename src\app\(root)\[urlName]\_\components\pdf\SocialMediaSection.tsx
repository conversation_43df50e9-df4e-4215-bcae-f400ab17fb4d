"use client";
import React from "react";
import { SocialAnalysis } from "@/types/seoAnalyzerTypes";
import {
  SectionHeader,
  SectionScoreBox,
  DataRow,
  RecommendationCard,
} from "./BaseComponents";
import { SectionWatermark } from "./WatermarkComponents";
import {
  FacebookIcon,
  LinkedinIcon,
  PinterestIcon,
  TelegramIcon,
  XIcon,
  YouTubeIcon,
} from "@/ui/icons/socialMedia";
import { InstagramColorIcon } from "@/ui/icons/socialMedia/InstagramColorIcon";
import { DocumentTextIcon, TagIcon } from "@/ui/icons/general";

export interface SocialMediaSectionProps {
  socialData: SocialAnalysis;
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

export const SocialMediaSection: React.FC<SocialMediaSectionProps> = ({
  socialData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  return (
    <div
      className="mb-8 print-section relative"
      data-watermark={brand_website || brand_name || "SEO ANALYSER"}
    >
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => onImageLoad?.("sectionLogoSocial")}
        onLogoError={() => onImageError?.("sectionLogoSocial")}
        sectionId="social-details"
      />

      <SectionHeader
        title="Social Media Audit "
        brandName={brand_name}
        brandWebsite={brand_website}
      />

      {/* Section Score Box */}
      {socialData.total_score && (
        <SectionScoreBox
          scoreGrade={socialData.total_score}
          title="Social Media Score"
          description={
            socialData.overall_description ||
            "This score evaluates your website's social media integration, including social platform connections, meta tags for social sharing, and overall social media presence."
          }
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Facebook Audit */}
        {socialData.facebook && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <FacebookIcon className="w-6 h-6 text-[#1877F2]" />
              <h3 className="font-bold text-gray-800 text-lg">
                Facebook Integration
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Facebook Connected"
                value={socialData.facebook.pass ? "✓ Yes" : "✗ No"}
              />
              {socialData.facebook.page_url && (
                <DataRow
                  label="Page URL"
                  value={socialData.facebook.page_url}
                />
              )}
              {socialData.facebook.pixel_id && (
                <DataRow
                  label="Pixel ID"
                  value={socialData.facebook.pixel_id}
                />
              )}
            </div>
            {socialData.facebook.og_tags &&
              Object.keys(socialData.facebook.og_tags).length > 0 && (
                <div className="mt-3">
                  <h4 className="font-medium text-gray-700 mb-2">
                    Open Graph Tags:
                  </h4>
                  <div className="space-y-1">
                    {/* Removed scrollbar - max-h-32 overflow-y-auto */}
                    {Object.entries(socialData.facebook.og_tags)
                      .slice(0, 5)
                      .map(([key, value], index) => (
                        <div
                          key={index}
                          className="text-xs bg-gray-50 p-2 rounded"
                        >
                          <span className="font-medium">{key}:</span>{" "}
                          {String(value)}
                        </div>
                      ))}
                  </div>
                </div>
              )}

            {/* Facebook Recommendation */}
            {socialData.facebook.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={socialData.facebook.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Twitter Audit */}
        {socialData.twitter && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <XIcon className="w-6 h-6 text-black" />
              <h3 className="font-bold text-gray-800 text-lg">
                Twitter Integration
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Twitter Connected"
                value={socialData.twitter.pass ? "✓ Yes" : "✗ No"}
              />
              {socialData.twitter.username && (
                <DataRow
                  label="Username"
                  value={`@${socialData.twitter.username}`}
                />
              )}
            </div>
            {socialData.twitter.twitter_cards &&
              Object.keys(socialData.twitter.twitter_cards).length > 0 && (
                <div className="mt-3">
                  <h4 className="font-medium text-gray-700 mb-2">
                    Twitter Cards:
                  </h4>
                  <div className="space-y-1">
                    {/* Removed scrollbar - max-h-32 overflow-y-auto */}
                    {Object.entries(socialData.twitter.twitter_cards)
                      .slice(0, 5)
                      .map(([key, value], index) => (
                        <div
                          key={index}
                          className="text-xs bg-gray-50 p-2 rounded"
                        >
                          <span className="font-medium">{key}:</span>{" "}
                          {String(value)}
                        </div>
                      ))}
                  </div>
                </div>
              )}

            {/* Twitter Recommendation */}
            {socialData.twitter.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={socialData.twitter.recommendation}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Instagram and LinkedIn sections moved to next page */}
      <div className="grid grid-cols-1 mt-4 lg:grid-cols-2 gap-6 ">
        {/* Instagram Audit */}
        {socialData.instagram && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <InstagramColorIcon className="w-6 h-6" />
              <h3 className="font-bold text-gray-800 text-lg">
                Instagram Integration
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Instagram Connected"
                value={socialData.instagram.pass ? "✓ Yes" : "✗ No"}
              />
              {socialData.instagram.profile_url && (
                <DataRow
                  label="Profile URL"
                  value={socialData.instagram.profile_url}
                />
              )}
            </div>
            {socialData.instagram.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {socialData.instagram.description}
                </p>
              </div>
            )}

            {/* Instagram Recommendation */}
            {socialData.instagram.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={socialData.instagram.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* LinkedIn Audit */}
        {socialData.linkedin && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <LinkedinIcon className="w-6 h-6 text-[#0A66C2]" />
              <h3 className="font-bold text-gray-800 text-lg">
                LinkedIn Integration
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="LinkedIn Connected"
                value={socialData.linkedin.pass ? "✓ Yes" : "✗ No"}
              />
              {socialData.linkedin.page_url && (
                <DataRow
                  label="Page URL"
                  value={socialData.linkedin.page_url}
                />
              )}
            </div>

            {/* LinkedIn Recommendation */}
            {socialData.linkedin.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={socialData.linkedin.recommendation}
                />
              </div>
            )}
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 mt-6 lg:grid-cols-2 gap-6">
        {/* YouTube Audit */}
        {socialData.youtube && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <YouTubeIcon className="w-6 h-6 text-[#FF0000]" />
              <h3 className="font-bold text-gray-800 text-lg">
                YouTube Integration
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="YouTube Connected"
                value={socialData.youtube.pass ? "✓ Yes" : "✗ No"}
              />
              {socialData.youtube.channel_url && (
                <DataRow
                  label="Channel URL"
                  value={socialData.youtube.channel_url}
                />
              )}
            </div>

            {/* YouTube Recommendation */}
            {socialData.youtube.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={socialData.youtube.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Social Meta Tags */}
        {socialData.social_meta_tags && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <TagIcon className="w-6 h-6 text-blue-600" />
              <h3 className="font-bold text-gray-800 text-lg">
                Social Meta Tags
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Meta Tags Present"
                value={socialData.social_meta_tags.pass ? "✓ Yes" : "✗ Missing"}
              />
            </div>
            {socialData.social_meta_tags.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {socialData.social_meta_tags.description}
                </p>
              </div>
            )}

            {/* Social Meta Tags Recommendation */}
            {socialData.social_meta_tags.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={socialData.social_meta_tags.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Share Buttons */}
        {socialData.share_buttons && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <DocumentTextIcon className="w-6 h-6 text-green-600" />
              <h3 className="font-bold text-gray-800 text-lg">
                Social Share Buttons
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Share Buttons Present"
                value={socialData.share_buttons.pass ? "✓ Yes" : "✗ Missing"}
              />
            </div>
            {socialData.share_buttons.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {socialData.share_buttons.description}
                </p>
              </div>
            )}

            {/* Share Buttons Recommendation */}
            {socialData.share_buttons.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={socialData.share_buttons.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Pinterest Audit */}
        {socialData.pinterest && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <PinterestIcon className="w-6 h-6 text-[#E60023]" />
              <h3 className="font-bold text-gray-800 text-lg">
                Pinterest Integration
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Pinterest Connected"
                value={socialData.pinterest.pass ? "✓ Yes" : "✗ No"}
              />
            </div>
            {socialData.pinterest.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {socialData.pinterest.description}
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Comprehensive Social Media Recommendations - Moved outside grid for full width */}
      <div className="mt-8">
        <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
          All Social Media Recommendations
        </h3>
        <div className="grid grid-cols-1 gap-4 recommendations-grid">
          {/* Facebook Recommendations */}
          {socialData.facebook?.recommendation && (
            <RecommendationCard
              recommendation={socialData.facebook.recommendation}
            />
          )}

          {/* Twitter Recommendations */}
          {socialData.twitter?.recommendation && (
            <RecommendationCard
              recommendation={socialData.twitter.recommendation}
            />
          )}

          {/* Open Graph Recommendations */}
          {socialData.open_graph?.recommendation && (
            <RecommendationCard
              recommendation={socialData.open_graph.recommendation}
            />
          )}

          {/* Twitter Cards Recommendations */}
          {socialData.twitter_cards?.recommendation && (
            <RecommendationCard
              recommendation={socialData.twitter_cards.recommendation}
            />
          )}

          {/* If no recommendations found */}
          {!socialData.facebook?.recommendation &&
            !socialData.twitter?.recommendation &&
            !socialData.open_graph?.recommendation &&
            !socialData.twitter_cards?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: "Your social media integration appears to be well configured. Continue optimizing social sharing and engagement.",
                  priority: "Low",
                }}
              />
            )}
        </div>
      </div>
    </div>
  );
};
