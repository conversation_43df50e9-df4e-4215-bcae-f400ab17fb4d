import LocalFont from "next/font/local";

const nunitoSansFont = LocalFont({
  src: [
    // Load only essential weights for better performance
    {
      path: "../../public/fonts/NunitoSans-Regular.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../../public/fonts/NunitoSans-Medium.woff2",
      weight: "500",
      style: "normal",
    },
    {
      path: "../../public/fonts/NunitoSans-SemiBold.woff2",
      weight: "600",
      style: "normal",
    },
    {
      path: "../../public/fonts/NunitoSans-Bold.woff2",
      weight: "700",
      style: "normal",
    },
  ],
  variable: "--font-nunito-sans",
  style: "normal",
  display: "swap", // ✅ Use swap for better performance
  preload: true,
});

export default nunitoSansFont;
