"use client";
import React, { useState } from "react";
import { LinksAnalysis } from "@/types/seoAnalyzerTypes";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SectionScoreBox,
  DataRow,
  RecommendationCard,
} from "./BaseComponents";
import { SectionWatermark } from "./WatermarkComponents";
import {
  CrownIcon,
  DocumentCrossIcon,
  CheckIcon,
  ChainIcon,
  CrossIcon,
  SparklesIcon,
} from "@/ui/icons/general";
import abbreviateNumber from "@/utils/abbreviateNumber";

// Import types from the Backlinks component
type LinksAnalysisData = {
  main?: {
    totalScore: {
      Grade: string;
      score: number;
    };
  };
  broken_links?: {
    metrics?: {
      broken_count_on_page?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      links_checked_on_page?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      broken_percentage_on_page?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
    };
    overall_title?: string;
    overall_description?: string;
    broken_analysis_summary?: string;
    broken_links_detail_sample?: Array<{
      url: string;
      status: string;
      error_code: number;
    }>;
  };
  domain_insight?: {
    metrics?: {
      spam_score?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      page_authority?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      domain_authority?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
    };
    overall_title?: string;
    overall_description?: string;
  };
  friendly_links?: {
    metrics?: {
      friendly_links_percentage?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      analyzed_text_links_on_page?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      unfriendly_links_percentage?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      unfriendly_links_count_sample?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
    };
    overall_title?: string;
    overall_description?: string;
    unfriendly_links_samples?: Array<{
      url: string;
      anchor_text: string;
      readability_score: number;
    }>;
    readability_analysis_summary?: string;
  };
  backlinks_detail?: {
    metrics?: {
      total?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      dofollow_links?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      nofollow_links?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      unique_domain_count?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
    };
    overall_title?: string;
    overall_description?: string;
    is_premium_user?: boolean;
    upgrade_message?: string;
    top_backlinks_sample?: Array<{
      anchor: string;
      dofollow: boolean;
      url_from: string;
      spam_score: number;
      domain_from: string;
      page_authority: number;
      domain_authority: number;
    }>;
  };
  overall_backlinks?: {
    metrics?: {
      total_backlinks?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      domain_authority?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      broken_links_count?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      friendly_links_percentage?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
    };
    overall_title?: string;
    overall_description?: string;
  };
};

type ActiveSectionType =
  | "overall_backlinks"
  | "backlinks_detail"
  | "domain_insight"
  | "friendly_links"
  | "broken_links";

// Utility functions for data formatting
const getMetricValue = (
  section: any,
  metricName: string,
  fallback: any = 0
) => {
  return section?.metrics?.[metricName]?.value ?? fallback;
};

const getMetricInfo = (section: any, metricName: string) => {
  const metric = section?.metrics?.[metricName];
  return {
    importance: metric?.importance,
    description: metric?.description,
    recommendation: metric?.recommendation,
  };
};

const formatMetricValue = (
  section: any,
  metricName: string,
  fallback: any = 0,
  formatType: "count" | "score" | "percentage" = "count"
) => {
  const value = getMetricValue(section, metricName, fallback);

  switch (formatType) {
    case "count":
      return typeof value === "number" ? abbreviateNumber(value) : value;
    case "score":
      return value.toString();
    case "percentage":
      return `${value}%`;
    default:
      return typeof value === "number"
        ? abbreviateNumber(value)
        : value.toString();
  }
};

const formatMetricValueWithPrefix = (
  section: any,
  metricName: string,
  fallback: any = 0,
  formatType: "count" | "score" | "percentage" = "count"
) => {
  const value = getMetricValue(section, metricName, fallback);
  const formattedValue =
    typeof value === "number" ? value.toLocaleString() : value;

  switch (formatType) {
    case "count":
      return formattedValue;
    case "score":
      return value.toString();
    case "percentage":
      return `${value}%`;
    default:
      return formattedValue;
  }
};

// PDF-suitable StateCard component
const PDFStateCard = ({
  icon,
  value,
  label,
}: {
  icon: React.ReactNode;
  value: string;
  label: string;
}) => (
  <div className="text-center p-4 bg-gradient-to-br from-primary/8 to-primary/5 rounded-xl border border-primary/20">
    <div className="flex justify-center mb-2 text-primary">{icon}</div>
    <div className="text-2xl font-bold text-primary mb-1">{value}</div>
    <div className="text-sm text-gray-600 font-medium">{label}</div>
  </div>
);

// PDF-suitable detailed section component
const PDFDetailSection = ({
  title,
  description,
  value,
  icon,
  children,
}: {
  title: string;
  description: string;
  value: string;
  icon: React.ReactNode;
  children?: React.ReactNode;
}) => (
  <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
    <div className="flex items-center gap-3 mb-4">
      <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
      <h4 className="font-bold text-gray-800 text-lg">{title}</h4>
    </div>

    <div className="flex items-start gap-4 mb-4">
      <div className="text-primary">{icon}</div>
      <div className="flex-1">
        <div className="text-3xl font-bold text-primary mb-2">{value}</div>
        <p className="text-sm text-gray-600 leading-relaxed">{description}</p>
      </div>
    </div>

    {children}
  </div>
);

export interface LinksSectionProps {
  linksData: LinksAnalysis;
  urlName: string;
  onPageSeoData?: {
    serp_preview?: {
      url?: string;
    };
  };
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

export const LinksSection: React.FC<LinksSectionProps> = ({
  linksData,
  urlName,
  onPageSeoData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  // State for active section (for PDF, we'll show all sections)
  const [activeSection, setActiveSection] =
    useState<ActiveSectionType>("overall_backlinks");

  // Parse linksData to match the new format
  let effectiveResults: LinksAnalysisData;

  if (!linksData) {
    effectiveResults = {};
  } else if (typeof linksData === "string") {
    try {
      effectiveResults = JSON.parse(linksData) as LinksAnalysisData;
    } catch {
      effectiveResults = {};
    }
  } else {
    // Convert old LinksAnalysis format to new LinksAnalysisData format
    effectiveResults = {
      main: linksData.main,
      // Convert old format fields to new format if they exist
      overall_backlinks: linksData.backlinks_detail
        ? {
            overall_title: linksData.backlinks_detail.overall_title,
            overall_description: linksData.backlinks_detail.overall_description,
            metrics: {
              total_backlinks: {
                value: linksData.backlinks?.total_backlinks || 0,
              },
              domain_authority: {
                value: linksData.domain_insight?.domain_authority || 0,
              },
              broken_links_count: {
                value: linksData.broken_links?.broken_count || 0,
              },
              friendly_links_percentage: {
                value: linksData.friendly_links?.friendly_percentage || 0,
              },
            },
          }
        : undefined,
      backlinks_detail: linksData.backlinks_detail
        ? {
            overall_title: linksData.backlinks_detail.overall_title,
            overall_description: linksData.backlinks_detail.overall_description,
            metrics: {
              total: { value: linksData.backlinks?.total_backlinks || 0 },
              dofollow_links: {
                value: linksData.backlinks_detail.dofollow_links || 0,
              },
              nofollow_links: {
                value: linksData.backlinks_detail.nofollow_links || 0,
              },
              unique_domain_count: {
                value: linksData.backlinks_detail.unique_domains || 0,
              },
            },
          }
        : undefined,
      domain_insight: linksData.domain_insight
        ? {
            overall_title: linksData.domain_insight.overall_title,
            overall_description: linksData.domain_insight.overall_description,
            metrics: {
              domain_authority: {
                value: linksData.domain_insight.domain_authority || 0,
              },
              page_authority: {
                value: linksData.domain_insight.page_authority || 0,
              },
              spam_score: { value: linksData.domain_insight.spam_score || 0 },
            },
          }
        : undefined,
      friendly_links: linksData.friendly_links
        ? {
            overall_title: "Friendly Links Audit",
            overall_description: linksData.friendly_links.description,
            metrics: {
              friendly_links_percentage: {
                value: linksData.friendly_links.friendly_percentage || 0,
              },
              analyzed_text_links_on_page: { value: 0 }, // Not available in old format
            },
            unfriendly_links_samples:
              linksData.friendly_links.unfriendly_links_sample?.map((link) => ({
                url: link.url,
                anchor_text: link.text,
                readability_score: link.score,
              })) || [],
          }
        : undefined,
      broken_links: linksData.broken_links
        ? {
            overall_title: "Broken Links Audit",
            overall_description: linksData.broken_links.description,
            metrics: {
              broken_count_on_page: {
                value: linksData.broken_links.broken_count || 0,
              },
              links_checked_on_page: {
                value: linksData.broken_links.total_checked || 0,
              },
              broken_percentage_on_page: {
                value: linksData.broken_links.broken_percentage || 0,
              },
            },
            broken_links_detail_sample:
              linksData.broken_links.sample_broken_links?.map((link) => ({
                url: link.url,
                status: link.status,
                error_code: link.details,
              })) || [],
          }
        : undefined,
    } as LinksAnalysisData;
  }

  const overallBacklinks = effectiveResults?.overall_backlinks;
  const backlinksDetail = effectiveResults?.backlinks_detail;
  const domainInsight = effectiveResults?.domain_insight;
  const friendlyLinks = effectiveResults?.friendly_links;
  const brokenLinks = effectiveResults?.broken_links;

  return (
    <div
      className="mb-8 print-section relative print:break-before-page"
      data-watermark={brand_website ?? brand_name ?? "SEO ANALYSER"}
    >
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => onImageLoad?.("sectionLogoLinks")}
        onLogoError={() => onImageError?.("sectionLogoLinks")}
        sectionId="links-details"
      />

      <SectionHeader
        title={`Links Audit`}
        brandName={brand_name}
        brandWebsite={brand_website}
      />

      {/* Section Score Box */}
      {(effectiveResults?.main?.totalScore ||
        (linksData as any)?.total_score) && (
        <SectionScoreBox
          scoreGrade={
            effectiveResults?.main?.totalScore ||
            (linksData as any)?.total_score
          }
          title="Links & Backlinks Score"
          description="This score evaluates your website's link profile, including internal linking structure, external backlinks quality, and overall link authority that impacts your search engine rankings."
        />
      )}

      <div className="space-y-8">
        {/* Overall Backlinks Overview */}
        {overallBacklinks && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                {overallBacklinks.overall_title || "Backlinks Overview"}
              </h3>
            </div>

            <p className="text-sm text-gray-600 mb-6 leading-relaxed bg-gray-50/50 p-3 rounded-lg">
              {overallBacklinks.overall_description ||
                "Overview of all link metrics including backlinks and more."}
            </p>

            {/* Key metrics cards */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <PDFStateCard
                icon={<ChainIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  overallBacklinks,
                  "total_backlinks",
                  0,
                  "count"
                )}
                label="Total Backlinks"
              />
              <PDFStateCard
                icon={<CrownIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  overallBacklinks,
                  "domain_authority",
                  0,
                  "score"
                )}
                label="Domain Authority"
              />
              <PDFStateCard
                icon={<DocumentCrossIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  overallBacklinks,
                  "broken_links_count",
                  0,
                  "count"
                )}
                label="Broken Links"
              />
              <PDFStateCard
                icon={<CheckIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  overallBacklinks,
                  "friendly_links_percentage",
                  0,
                  "percentage"
                )}
                label="Friendly Links"
              />
            </div>

            {/* Detailed metrics */}
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Total Backlinks"
                value={formatMetricValueWithPrefix(
                  overallBacklinks,
                  "total_backlinks",
                  0,
                  "count"
                )}
                important={true}
              />
              <DataRow
                label="Domain Authority"
                value={formatMetricValueWithPrefix(
                  overallBacklinks,
                  "domain_authority",
                  0,
                  "score"
                )}
                important={true}
              />
              <DataRow
                label="Broken Links"
                value={formatMetricValueWithPrefix(
                  overallBacklinks,
                  "broken_links_count",
                  0,
                  "count"
                )}
                important={
                  getMetricValue(overallBacklinks, "broken_links_count", 0) > 0
                }
              />
              <DataRow
                label="Friendly Links"
                value={formatMetricValueWithPrefix(
                  overallBacklinks,
                  "friendly_links_percentage",
                  0,
                  "percentage"
                )}
              />
            </div>

            {/* Overall Backlinks Recommendations */}
            <div className="mt-4 space-y-3">
              {overallBacklinks.metrics?.total_backlinks?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Total Backlinks Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: overallBacklinks.metrics.total_backlinks
                        .recommendation,
                      priority:
                        overallBacklinks.metrics.total_backlinks.importance ===
                          "High" ||
                        overallBacklinks.metrics.total_backlinks.importance ===
                          "Low"
                          ? overallBacklinks.metrics.total_backlinks.importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {overallBacklinks.metrics?.domain_authority?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Domain Authority Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: overallBacklinks.metrics.domain_authority
                        .recommendation,
                      priority:
                        overallBacklinks.metrics.domain_authority.importance ===
                          "High" ||
                        overallBacklinks.metrics.domain_authority.importance ===
                          "Low"
                          ? overallBacklinks.metrics.domain_authority.importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {overallBacklinks.metrics?.broken_links_count?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Broken Links Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: overallBacklinks.metrics.broken_links_count
                        .recommendation,
                      priority:
                        overallBacklinks.metrics.broken_links_count
                          .importance === "High" ||
                        overallBacklinks.metrics.broken_links_count
                          .importance === "Low"
                          ? overallBacklinks.metrics.broken_links_count
                              .importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {overallBacklinks.metrics?.friendly_links_percentage
                ?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Friendly Links Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: overallBacklinks.metrics.friendly_links_percentage
                        .recommendation,
                      priority:
                        overallBacklinks.metrics.friendly_links_percentage
                          .importance === "High" ||
                        overallBacklinks.metrics.friendly_links_percentage
                          .importance === "Low"
                          ? overallBacklinks.metrics.friendly_links_percentage
                              .importance
                          : "Medium",
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Backlinks Detail */}
        {backlinksDetail && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                {backlinksDetail.overall_title || "Backlinks Detail"}
              </h3>
            </div>

            <p className="text-sm text-gray-600 mb-6 leading-relaxed bg-gray-50/50 p-3 rounded-lg">
              {backlinksDetail.overall_description ||
                "Detailed backlink audit helps understand link quality and sources."}
            </p>

            {/* Key metrics cards */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <PDFStateCard
                icon={<ChainIcon className="w-6 h-6" />}
                value={formatMetricValue(backlinksDetail, "total", 0, "count")}
                label="Total Backlinks"
              />
              <PDFStateCard
                icon={<SparklesIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  backlinksDetail,
                  "unique_domain_count",
                  0,
                  "count"
                )}
                label="Unique Domains"
              />
              <PDFStateCard
                icon={<CheckIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  backlinksDetail,
                  "dofollow_links",
                  0,
                  "count"
                )}
                label="Dofollow Links"
              />
              <PDFStateCard
                icon={<CrossIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  backlinksDetail,
                  "nofollow_links",
                  0,
                  "count"
                )}
                label="Nofollow Links"
              />
            </div>

            {/* Detailed metrics */}
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Total Backlinks"
                value={formatMetricValueWithPrefix(
                  backlinksDetail,
                  "total",
                  0,
                  "count"
                )}
              />
              <DataRow
                label="Unique Domains"
                value={formatMetricValueWithPrefix(
                  backlinksDetail,
                  "unique_domain_count",
                  0,
                  "count"
                )}
              />
              <DataRow
                label="Dofollow Links"
                value={formatMetricValueWithPrefix(
                  backlinksDetail,
                  "dofollow_links",
                  0,
                  "count"
                )}
              />
              <DataRow
                label="Nofollow Links"
                value={formatMetricValueWithPrefix(
                  backlinksDetail,
                  "nofollow_links",
                  0,
                  "count"
                )}
              />
            </div>

            {/* Premium user check */}
            {backlinksDetail?.is_premium_user === false && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  {backlinksDetail?.upgrade_message ||
                    "Upgrade to Pro plan for more detailed backlink information and larger samples."}
                </p>
              </div>
            )}

            {/* Top Backlinks Sample Table */}
            {backlinksDetail?.top_backlinks_sample &&
              backlinksDetail.top_backlinks_sample.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium text-gray-900 mb-3">
                    Top Backlinks Sample
                  </h4>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className="text-left py-2 px-3 font-medium text-gray-700">
                            Domain
                          </th>
                          <th className="text-left py-2 px-3 font-medium text-gray-700">
                            Anchor Text
                          </th>
                          <th className="text-center py-2 px-3 font-medium text-gray-700">
                            Type
                          </th>
                          <th className="text-center py-2 px-3 font-medium text-gray-700">
                            DA
                          </th>
                          <th className="text-center py-2 px-3 font-medium text-gray-700">
                            PA
                          </th>
                          <th className="text-center py-2 px-3 font-medium text-gray-700">
                            Spam Score
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {backlinksDetail.top_backlinks_sample.map(
                          (item, index) => (
                            <tr
                              key={index}
                              className="border-b border-gray-100"
                            >
                              <td className="py-2 px-3 text-primary font-medium break-words max-w-[200px]">
                                {item.domain_from}
                              </td>
                              <td className="py-2 px-3 break-words max-w-[200px]">
                                {item.anchor || "N/A"}
                              </td>
                              <td className="py-2 px-3 text-center">
                                <span
                                  className={`px-2 py-1 rounded text-xs ${
                                    item.dofollow
                                      ? "bg-green-100 text-green-800"
                                      : "bg-gray-100 text-gray-800"
                                  }`}
                                >
                                  {item.dofollow ? "Dofollow" : "Nofollow"}
                                </span>
                              </td>
                              <td className="py-2 px-3 text-center">
                                {item.domain_authority}
                              </td>
                              <td className="py-2 px-3 text-center">
                                {item.page_authority}
                              </td>
                              <td className="py-2 px-3 text-center">
                                {item.spam_score}
                              </td>
                            </tr>
                          )
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

            {/* Backlinks Detail Recommendations */}
            <div className="mt-4 space-y-3">
              {backlinksDetail.metrics?.total?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Total Backlinks Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: backlinksDetail.metrics.total.recommendation,
                      priority:
                        backlinksDetail.metrics.total.importance === "High" ||
                        backlinksDetail.metrics.total.importance === "Low"
                          ? backlinksDetail.metrics.total.importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {backlinksDetail.metrics?.unique_domain_count?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Unique Domains Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: backlinksDetail.metrics.unique_domain_count
                        .recommendation,
                      priority:
                        backlinksDetail.metrics.unique_domain_count
                          .importance === "High" ||
                        backlinksDetail.metrics.unique_domain_count
                          .importance === "Low"
                          ? backlinksDetail.metrics.unique_domain_count
                              .importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {backlinksDetail.metrics?.dofollow_links?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Dofollow Links Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: backlinksDetail.metrics.dofollow_links
                        .recommendation,
                      priority:
                        backlinksDetail.metrics.dofollow_links.importance ===
                          "High" ||
                        backlinksDetail.metrics.dofollow_links.importance ===
                          "Low"
                          ? backlinksDetail.metrics.dofollow_links.importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {backlinksDetail.metrics?.nofollow_links?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Nofollow Links Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: backlinksDetail.metrics.nofollow_links
                        .recommendation,
                      priority:
                        backlinksDetail.metrics.nofollow_links.importance ===
                          "High" ||
                        backlinksDetail.metrics.nofollow_links.importance ===
                          "Low"
                          ? backlinksDetail.metrics.nofollow_links.importance
                          : "Medium",
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Domain Insight */}
        {domainInsight && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                {domainInsight.overall_title || "Domain Authority Insight"}
              </h3>
            </div>

            <p className="text-sm text-gray-600 mb-6 leading-relaxed bg-gray-50/50 p-3 rounded-lg">
              {domainInsight.overall_description ||
                "Domain authority and page metrics audit."}
            </p>

            {/* Key metrics cards */}
            <div className="grid grid-cols-3 gap-4 mb-6">
              <PDFStateCard
                icon={<CrownIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  domainInsight,
                  "domain_authority",
                  0,
                  "score"
                )}
                label="Domain Authority"
              />
              <PDFStateCard
                icon={<SparklesIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  domainInsight,
                  "page_authority",
                  0,
                  "score"
                )}
                label="Page Authority"
              />
              <PDFStateCard
                icon={<DocumentCrossIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  domainInsight,
                  "spam_score",
                  0,
                  "percentage"
                )}
                label="Spam Score"
              />
            </div>

            {/* Detailed metrics */}
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Domain Authority"
                value={formatMetricValueWithPrefix(
                  domainInsight,
                  "domain_authority",
                  0,
                  "score"
                )}
              />
              <DataRow
                label="Page Authority"
                value={formatMetricValueWithPrefix(
                  domainInsight,
                  "page_authority",
                  0,
                  "score"
                )}
              />
              <DataRow
                label="Spam Score"
                value={formatMetricValueWithPrefix(
                  domainInsight,
                  "spam_score",
                  0,
                  "percentage"
                )}
              />
            </div>

            {/* Domain Insight Recommendations */}
            <div className="mt-4 space-y-3">
              {domainInsight.metrics?.domain_authority?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Domain Authority Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: domainInsight.metrics.domain_authority
                        .recommendation,
                      priority:
                        domainInsight.metrics.domain_authority.importance ===
                          "High" ||
                        domainInsight.metrics.domain_authority.importance ===
                          "Low"
                          ? domainInsight.metrics.domain_authority.importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {domainInsight.metrics?.page_authority?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Page Authority Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: domainInsight.metrics.page_authority.recommendation,
                      priority:
                        domainInsight.metrics.page_authority.importance ===
                          "High" ||
                        domainInsight.metrics.page_authority.importance ===
                          "Low"
                          ? domainInsight.metrics.page_authority.importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {domainInsight.metrics?.spam_score?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Spam Score Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: domainInsight.metrics.spam_score.recommendation,
                      priority:
                        domainInsight.metrics.spam_score.importance ===
                          "High" ||
                        domainInsight.metrics.spam_score.importance === "Low"
                          ? domainInsight.metrics.spam_score.importance
                          : "Medium",
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Friendly Links */}
        {friendlyLinks && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                {friendlyLinks.overall_title || "Friendly Links Audit"}
              </h3>
            </div>

            <p className="text-sm text-gray-600 mb-6 leading-relaxed bg-gray-50/50 p-3 rounded-lg">
              {friendlyLinks.overall_description ||
                "Audit of anchor text readability and user-friendly link text."}
            </p>

            {/* Readability Audit Summary */}
            {friendlyLinks.readability_analysis_summary && (
              <div className="mb-6 p-4 bg-blue-50/50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">
                  Readability Audit Summary
                </h4>
                <p className="text-sm text-blue-800 leading-relaxed">
                  {friendlyLinks.readability_analysis_summary}
                </p>
              </div>
            )}

            {/* Key metrics cards */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <PDFStateCard
                icon={<CheckIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  friendlyLinks,
                  "friendly_links_percentage",
                  0,
                  "percentage"
                )}
                label="Friendly Links"
              />
              <PDFStateCard
                icon={<CrossIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  friendlyLinks,
                  "unfriendly_links_percentage",
                  0,
                  "percentage"
                )}
                label="Unfriendly Links"
              />
              <PDFStateCard
                icon={<ChainIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  friendlyLinks,
                  "analyzed_text_links_on_page",
                  0,
                  "count"
                )}
                label="Links Analyzed"
              />
              <PDFStateCard
                icon={<CrossIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  friendlyLinks,
                  "unfriendly_links_count_sample",
                  0,
                  "count"
                )}
                label="Unfriendly Samples"
              />
            </div>

            {/* Detailed metrics */}
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Friendly Links Percentage"
                value={formatMetricValueWithPrefix(
                  friendlyLinks,
                  "friendly_links_percentage",
                  0,
                  "percentage"
                )}
              />
              <DataRow
                label="Unfriendly Links Percentage"
                value={formatMetricValueWithPrefix(
                  friendlyLinks,
                  "unfriendly_links_percentage",
                  0,
                  "percentage"
                )}
              />
              <DataRow
                label="Links Analyzed"
                value={formatMetricValueWithPrefix(
                  friendlyLinks,
                  "analyzed_text_links_on_page",
                  0,
                  "count"
                )}
              />
              <DataRow
                label="Unfriendly Links Sample Count"
                value={formatMetricValueWithPrefix(
                  friendlyLinks,
                  "unfriendly_links_count_sample",
                  0,
                  "count"
                )}
              />
            </div>

            {/* Unfriendly Links Sample */}
            {friendlyLinks?.unfriendly_links_samples &&
              friendlyLinks.unfriendly_links_samples.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium text-gray-900 mb-3">
                    Unfriendly Links Sample
                  </h4>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className="text-left py-2 px-3 font-medium text-gray-700">
                            URL
                          </th>
                          <th className="text-left py-2 px-3 font-medium text-gray-700">
                            Anchor Text
                          </th>
                          <th className="text-center py-2 px-3 font-medium text-gray-700">
                            Readability Score
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {friendlyLinks.unfriendly_links_samples.map(
                          (item, index) => (
                            <tr
                              key={index}
                              className="border-b border-gray-100"
                            >
                              <td className="py-2 px-3 text-primary font-medium break-words max-w-[300px]">
                                {item.url}
                              </td>
                              <td className="py-2 px-3 break-words max-w-[200px]">
                                {item.anchor_text}
                              </td>
                              <td className="py-2 px-3 text-center">
                                <span
                                  className={`px-2 py-1 rounded text-xs ${
                                    item.readability_score < 50
                                      ? "bg-red-100 text-red-800"
                                      : item.readability_score < 75
                                      ? "bg-yellow-100 text-yellow-800"
                                      : "bg-green-100 text-green-800"
                                  }`}
                                >
                                  {item.readability_score}
                                </span>
                              </td>
                            </tr>
                          )
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

            {/* Friendly Links Recommendations */}
            <div className="mt-4 space-y-3">
              {friendlyLinks.metrics?.friendly_links_percentage
                ?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Friendly Links Percentage Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: friendlyLinks.metrics.friendly_links_percentage
                        .recommendation,
                      priority:
                        friendlyLinks.metrics.friendly_links_percentage
                          .importance === "High" ||
                        friendlyLinks.metrics.friendly_links_percentage
                          .importance === "Low"
                          ? friendlyLinks.metrics.friendly_links_percentage
                              .importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {friendlyLinks.metrics?.unfriendly_links_percentage
                ?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Unfriendly Links Percentage Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: friendlyLinks.metrics.unfriendly_links_percentage
                        .recommendation,
                      priority:
                        friendlyLinks.metrics.unfriendly_links_percentage
                          .importance === "High" ||
                        friendlyLinks.metrics.unfriendly_links_percentage
                          .importance === "Low"
                          ? friendlyLinks.metrics.unfriendly_links_percentage
                              .importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {friendlyLinks.metrics?.analyzed_text_links_on_page
                ?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Analyzed Text Links Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: friendlyLinks.metrics.analyzed_text_links_on_page
                        .recommendation,
                      priority:
                        friendlyLinks.metrics.analyzed_text_links_on_page
                          .importance === "High" ||
                        friendlyLinks.metrics.analyzed_text_links_on_page
                          .importance === "Low"
                          ? friendlyLinks.metrics.analyzed_text_links_on_page
                              .importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {friendlyLinks.metrics?.unfriendly_links_count_sample
                ?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Unfriendly Links Sample Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: friendlyLinks.metrics.unfriendly_links_count_sample
                        .recommendation,
                      priority:
                        friendlyLinks.metrics.unfriendly_links_count_sample
                          .importance === "High" ||
                        friendlyLinks.metrics.unfriendly_links_count_sample
                          .importance === "Low"
                          ? friendlyLinks.metrics.unfriendly_links_count_sample
                              .importance
                          : "Medium",
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Broken Links */}
        {brokenLinks && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                {brokenLinks.overall_title || "Broken Links Audit"}
              </h3>
            </div>

            <p className="text-sm text-gray-600 mb-6 leading-relaxed bg-gray-50/50 p-3 rounded-lg">
              {brokenLinks.overall_description ||
                "Audit of broken links on the page."}
            </p>

            {/* Broken Audit Summary */}
            {brokenLinks.broken_analysis_summary && (
              <div className="mb-6 p-4 bg-red-50/50 border border-red-200 rounded-lg">
                <h4 className="font-medium text-red-900 mb-2">
                  Broken Links Audit Summary
                </h4>
                <p className="text-sm text-red-800 leading-relaxed">
                  {brokenLinks.broken_analysis_summary}
                </p>
              </div>
            )}

            {/* Key metrics cards */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <PDFStateCard
                icon={<ChainIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  brokenLinks,
                  "links_checked_on_page",
                  0,
                  "count"
                )}
                label="Links Checked"
              />
              <PDFStateCard
                icon={<DocumentCrossIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  brokenLinks,
                  "broken_count_on_page",
                  0,
                  "count"
                )}
                label="Broken Links"
              />
              <PDFStateCard
                icon={<CrossIcon className="w-6 h-6" />}
                value={formatMetricValue(
                  brokenLinks,
                  "broken_percentage_on_page",
                  0,
                  "percentage"
                )}
                label="Broken Percentage"
              />
              <PDFStateCard
                icon={<SparklesIcon className="w-6 h-6" />}
                value={
                  brokenLinks?.broken_links_detail_sample?.length?.toString() ||
                  "0"
                }
                label="Sample Count"
              />
            </div>

            {/* Detailed metrics */}
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Links Checked"
                value={formatMetricValueWithPrefix(
                  brokenLinks,
                  "links_checked_on_page",
                  0,
                  "count"
                )}
              />
              <DataRow
                label="Broken Links"
                value={formatMetricValueWithPrefix(
                  brokenLinks,
                  "broken_count_on_page",
                  0,
                  "count"
                )}
              />
              <DataRow
                label="Broken Percentage"
                value={formatMetricValueWithPrefix(
                  brokenLinks,
                  "broken_percentage_on_page",
                  0,
                  "percentage"
                )}
              />
              <DataRow
                label="Sample Count"
                value={
                  brokenLinks?.broken_links_detail_sample?.length?.toString() ||
                  "0"
                }
              />
            </div>

            {/* Broken Links Detail Sample */}
            {brokenLinks?.broken_links_detail_sample &&
              brokenLinks.broken_links_detail_sample.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium text-gray-900 mb-3">
                    Broken Links Sample
                  </h4>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className="text-left py-2 px-3 font-medium text-gray-700">
                            URL
                          </th>
                          <th className="text-center py-2 px-3 font-medium text-gray-700">
                            Status
                          </th>
                          <th className="text-center py-2 px-3 font-medium text-gray-700">
                            Error Code
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {brokenLinks.broken_links_detail_sample.map(
                          (item, index) => (
                            <tr
                              key={index}
                              className="border-b border-gray-100"
                            >
                              <td className="py-2 px-3 text-primary font-medium break-words max-w-[400px]">
                                {item.url}
                              </td>
                              <td className="py-2 px-3 text-center">
                                <span className="px-2 py-1 rounded text-xs bg-red-100 text-red-800">
                                  {item.status}
                                </span>
                              </td>
                              <td className="py-2 px-3 text-center font-mono">
                                {item.error_code}
                              </td>
                            </tr>
                          )
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

            {/* Broken Links Recommendations */}
            <div className="mt-4 space-y-3">
              {brokenLinks.metrics?.broken_count_on_page?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Broken Links Count Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: brokenLinks.metrics.broken_count_on_page
                        .recommendation,
                      priority:
                        brokenLinks.metrics.broken_count_on_page.importance ===
                          "High" ||
                        brokenLinks.metrics.broken_count_on_page.importance ===
                          "Low"
                          ? brokenLinks.metrics.broken_count_on_page.importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {brokenLinks.metrics?.links_checked_on_page?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Links Checked Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: brokenLinks.metrics.links_checked_on_page
                        .recommendation,
                      priority:
                        brokenLinks.metrics.links_checked_on_page.importance ===
                          "High" ||
                        brokenLinks.metrics.links_checked_on_page.importance ===
                          "Low"
                          ? brokenLinks.metrics.links_checked_on_page.importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {brokenLinks.metrics?.broken_percentage_on_page
                ?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Broken Percentage Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: brokenLinks.metrics.broken_percentage_on_page
                        .recommendation,
                      priority:
                        brokenLinks.metrics.broken_percentage_on_page
                          .importance === "High" ||
                        brokenLinks.metrics.broken_percentage_on_page
                          .importance === "Low"
                          ? brokenLinks.metrics.broken_percentage_on_page
                              .importance
                          : "Medium",
                    }}
                  />
                </div>
              )}

              {brokenLinks.metrics?.links_checked_on_page?.recommendation && (
                <div className="border-t border-gray-200 pt-3">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Links Checked Recommendation:
                  </h5>
                  <RecommendationCard
                    recommendation={{
                      text: brokenLinks.metrics.links_checked_on_page
                        .recommendation,
                      priority:
                        brokenLinks.metrics.links_checked_on_page.importance ===
                          "High" ||
                        brokenLinks.metrics.links_checked_on_page.importance ===
                          "Low"
                          ? brokenLinks.metrics.links_checked_on_page.importance
                          : "Medium",
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Comprehensive Links Recommendations */}
        <div className="mt-6">
          <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
            Links Audit Recommendations
          </h3>
          <div className="grid grid-cols-1 gap-4 recommendations-grid">
            {/* Backlinks Recommendations from old format */}
            {(linksData as any).backlinks?.recommendation && (
              <RecommendationCard
                recommendation={(linksData as any).backlinks.recommendation}
              />
            )}

            {/* Friendly Links Recommendations from old format */}
            {(linksData as any).friendly_links?.recommendation && (
              <RecommendationCard
                recommendation={
                  (linksData as any).friendly_links.recommendation
                }
              />
            )}

            {/* Broken Links Recommendations from old format */}
            {(linksData as any).broken_links?.recommendation && (
              <RecommendationCard
                recommendation={(linksData as any).broken_links.recommendation}
              />
            )}

            {/* On Page Links Recommendations from old format */}
            {(linksData as any).on_page_links?.recommendation && (
              <RecommendationCard
                recommendation={(linksData as any).on_page_links.recommendation}
              />
            )}

            {/* Domain Insight Recommendations from old format */}
            {(linksData as any).domain_insight?.recommendation && (
              <RecommendationCard
                recommendation={
                  (linksData as any).domain_insight.recommendation
                }
              />
            )}

            {/* Recommendations from metrics in new format */}
            {overallBacklinks?.metrics?.total_backlinks?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: overallBacklinks.metrics.total_backlinks.recommendation,
                  priority:
                    (overallBacklinks.metrics.total_backlinks.importance as
                      | "High"
                      | "Medium"
                      | "Low") || "Medium",
                }}
              />
            )}

            {backlinksDetail?.metrics?.total?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: backlinksDetail.metrics.total.recommendation,
                  priority:
                    (backlinksDetail.metrics.total.importance as
                      | "High"
                      | "Medium"
                      | "Low") || "Medium",
                }}
              />
            )}

            {domainInsight?.metrics?.domain_authority?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: domainInsight.metrics.domain_authority.recommendation,
                  priority:
                    (domainInsight.metrics.domain_authority.importance as
                      | "High"
                      | "Medium"
                      | "Low") || "Medium",
                }}
              />
            )}

            {friendlyLinks?.metrics?.friendly_links_percentage
              ?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: friendlyLinks.metrics.friendly_links_percentage
                    .recommendation,
                  priority:
                    (friendlyLinks.metrics.friendly_links_percentage
                      .importance as "High" | "Medium" | "Low") || "Medium",
                }}
              />
            )}

            {brokenLinks?.metrics?.broken_count_on_page?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: brokenLinks.metrics.broken_count_on_page.recommendation,
                  priority:
                    (brokenLinks.metrics.broken_count_on_page.importance as
                      | "High"
                      | "Medium"
                      | "Low") || "High",
                }}
              />
            )}

            {/* Default recommendation if no specific recommendations found */}
            {!(linksData as any).backlinks?.recommendation &&
              !(linksData as any).friendly_links?.recommendation &&
              !(linksData as any).broken_links?.recommendation &&
              !(linksData as any).on_page_links?.recommendation &&
              !(linksData as any).domain_insight?.recommendation &&
              !overallBacklinks?.metrics?.total_backlinks?.recommendation &&
              !backlinksDetail?.metrics?.total?.recommendation &&
              !domainInsight?.metrics?.domain_authority?.recommendation &&
              !friendlyLinks?.metrics?.friendly_links_percentage
                ?.recommendation &&
              !brokenLinks?.metrics?.broken_count_on_page?.recommendation && (
                <RecommendationCard
                  recommendation={{
                    text: "Your website's link structure appears to be well optimized. Continue building quality backlinks and maintaining internal link structure.",
                    priority: "Low",
                  }}
                />
              )}
          </div>
        </div>
      </div>
    </div>
  );
};
