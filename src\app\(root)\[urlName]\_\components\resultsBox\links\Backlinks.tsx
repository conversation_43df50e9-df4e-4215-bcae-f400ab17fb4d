import BoxPrimary from "../../BoxPrimary";
import { useState } from "react";
import { LinksAnalysis } from "@/types/seoAnalyzerTypes";
import LoadingSlate from "@/components/loading/LoadingSlate";
import { motion, AnimatePresence } from "framer-motion";
import sampleDomainInsightData from "@/data/sample-domain-insight.json";

// Import modular tab components
import OverviewTab from "./OverviewTab";
import BacklinksDetailTab from "./BacklinksDetailTab";
import DomainInsightTab from "./DomainInsightTab";
import FriendlyLinksTab from "./FriendlyLinksTab";
import BrokenLinksTab from "./BrokenLinksTab";

// Import shared types and utilities
import { LinksAnalysisData, ActiveSectionType } from "./types";
import { getActiveData } from "./utils";

type BacklinksProps = {
  results?: LinksAnalysisData | LinksAnalysis | string | null;
};

export default function Backlinks({ results }: BacklinksProps = {}) {
  // State to track which section is active - default to overall_backlinks to showcase the feature
  const [activeSection, setActiveSection] =
    useState<ActiveSectionType>("overall_backlinks");

  try {
    // Handle the new data format
    let effectiveResults: LinksAnalysisData;

    if (!results) {
      // Use sample data if no results provided
      effectiveResults = sampleDomainInsightData.result
        .links as LinksAnalysisData;
    } else if (typeof results === "string") {
      try {
        effectiveResults = JSON.parse(results) as LinksAnalysisData;
      } catch {
        effectiveResults = {};
      }
    } else {
      effectiveResults = results as LinksAnalysisData;
    }

    // Animation variants for tab transitions - fast and smooth animations
    const tabContentVariants = {
      hidden: { opacity: 0, x: -5 },
      visible: {
        opacity: 1,
        x: 0,
        transition: {
          duration: 0.15,
          ease: [0.25, 0.1, 0.25, 1.0],
          staggerChildren: 0.02,
        },
      },
      exit: {
        opacity: 0,
        x: 5,
        transition: {
          duration: 0.1,
          ease: [0.25, 0.1, 0.25, 1.0],
        },
      },
    };

    const itemVariants = {
      hidden: { opacity: 0, y: 5 },
      visible: {
        opacity: 1,
        y: 0,
        transition: {
          duration: 0.15,
          ease: [0.25, 0.1, 0.25, 1.0],
        },
      },
    };

    // Check if we have any data to show (even partial)
    const hasAnyData =
      effectiveResults && Object.keys(effectiveResults).length > 0;

    // If we don't have any data at all, show loading state
    if (!hasAnyData) {
      return (
        <BoxPrimary title="Backlinks">
          <LoadingSlate
            title="Loading links results..."
            showHeader={true}
            showCards={true}
            cardCount={4}
            showChart={false}
            showProgress={true}
            height="lg"
          />
        </BoxPrimary>
      );
    }

    // Get the active data based on the selected section
    const activeData = getActiveData(activeSection, effectiveResults);

    return (
      <BoxPrimary title="Backlinks">
        {/* Sample data indicator */}
        {!results && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm font-medium text-blue-700">
                Showcasing Links Analysis with Sample Data
              </span>
            </div>
            <p className="text-xs text-blue-600 mt-1">
              This demonstrates the links analysis functionality with realistic
              sample data.
            </p>
          </div>
        )}

        {/* Section tabs */}
        <div className="flex border-b border-light-gray mb-3 sm:mb-4 w-full overflow-x-auto">
          {/* Tab buttons with motion animations */}
          <motion.button
            className={`px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${
              activeSection === "overall_backlinks"
                ? "text-primary border-b-2 border-primary"
                : "text-secondary/60"
            }`}
            onClick={() => setActiveSection("overall_backlinks")}
            whileHover={{
              backgroundColor: "rgba(145, 74, 196, 0.05)",
              transition: { duration: 0.15 },
            }}
            whileTap={{ scale: 0.97 }}
          >
            Overview
          </motion.button>
          <motion.button
            className={`px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${
              activeSection === "backlinks_detail"
                ? "text-primary border-b-2 border-primary"
                : "text-secondary/60"
            }`}
            onClick={() => setActiveSection("backlinks_detail")}
            whileHover={{
              backgroundColor: "rgba(145, 74, 196, 0.05)",
              transition: { duration: 0.15 },
            }}
            whileTap={{ scale: 0.97 }}
          >
            Backlinks
          </motion.button>
          <motion.button
            className={`px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${
              activeSection === "domain_insight"
                ? "text-primary border-b-2 border-primary"
                : "text-secondary/60"
            }`}
            onClick={() => setActiveSection("domain_insight")}
            whileHover={{
              backgroundColor: "rgba(145, 74, 196, 0.05)",
              transition: { duration: 0.15 },
            }}
            whileTap={{ scale: 0.97 }}
          >
            Domain Insight
          </motion.button>
          <motion.button
            className={`px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${
              activeSection === "friendly_links"
                ? "text-primary border-b-2 border-primary"
                : "text-secondary/60"
            }`}
            onClick={() => setActiveSection("friendly_links")}
            whileHover={{
              backgroundColor: "rgba(145, 74, 196, 0.05)",
              transition: { duration: 0.15 },
            }}
            whileTap={{ scale: 0.97 }}
          >
            Friendly Links
          </motion.button>
          <motion.button
            className={`px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${
              activeSection === "broken_links"
                ? "text-primary border-b-2 border-primary"
                : "text-secondary/60"
            }`}
            onClick={() => setActiveSection("broken_links")}
            whileHover={{
              backgroundColor: "rgba(145, 74, 196, 0.05)",
              transition: { duration: 0.15 },
            }}
            whileTap={{ scale: 0.97 }}
          >
            Broken Links
          </motion.button>
        </div>

        <div className="flex flex-col gap-3 sm:gap-4 lg:gap-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeSection}
              variants={tabContentVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="flex flex-col gap-3 sm:gap-4 lg:gap-6 w-full"
            >
              {activeSection === "overall_backlinks" && (
                <OverviewTab
                  data={effectiveResults}
                  itemVariants={itemVariants}
                />
              )}
              {activeSection === "backlinks_detail" && (
                <BacklinksDetailTab
                  data={effectiveResults}
                  itemVariants={itemVariants}
                />
              )}
              {activeSection === "domain_insight" && (
                <DomainInsightTab
                  data={effectiveResults}
                  itemVariants={itemVariants}
                />
              )}
              {activeSection === "friendly_links" && (
                <FriendlyLinksTab
                  data={effectiveResults}
                  itemVariants={itemVariants}
                />
              )}
              {activeSection === "broken_links" && (
                <BrokenLinksTab
                  data={effectiveResults}
                  itemVariants={itemVariants}
                />
              )}
            </motion.div>
          </AnimatePresence>
        </div>
      </BoxPrimary>
    );
  } catch (error) {
    console.error("Error in Link component:", error);
    return (
      <BoxPrimary title="Backlinks">
        <div className="bg-white rounded-lg border border-light-gray p-6">
          <div className="text-center py-8">
            <div className="text-red-500 text-4xl mb-4">⚠️</div>
            <h3 className="text-lg font-semibold text-red-700 mb-2">
              Error Loading Backlinks Data
            </h3>
            <p className="text-red-600">
              Something went wrong while loading the backlinks analysis. Please
              try again later.
            </p>
          </div>
        </div>
      </BoxPrimary>
    );
  }
}
