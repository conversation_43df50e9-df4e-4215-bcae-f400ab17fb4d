import Table from "@/ui/Table";

type Props = {
  data: { labData: string; value: string }[];
  title?: string;
};

export default function UsabilityTable({ data, title }: Props) {
  return (
    <div className="w-full pagespeed-table">
      {/* {title && <h5 className="font-bold text-secondary mb-2">{title}</h5>} */}
      <Table>
        <Table.Header>
          <th className="!text-sm sm:!text-base font-semibold">
            {title ? title : "Usability Table"}
          </th>
          <th className="!text-sm sm:!text-base text-end pr-2 sm:pr-4">
            Value
          </th>
        </Table.Header>
        <Table.Body>
          {data.map((item, index) => (
            <Table.Row key={index}>
              <td className="!text-xs sm:!text-sm">{item.labData}</td>
              <td className="text-end !text-xs sm:text-sm">{item.value}s</td>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </div>
  );
}
