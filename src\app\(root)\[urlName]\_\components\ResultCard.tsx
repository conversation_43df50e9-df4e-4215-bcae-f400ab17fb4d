"use client";
import { DocumentCheckIcon, DocumentCrossIcon } from "@/ui/icons/general";
import FeedbackCard from "./resultsBox/FeedbackCard";
import { ReactNode, useState } from "react";

type Props = {
  title: string;
  description: string | ReactNode;
  passed?: boolean;
  btnLink?: string;
  className?: string;
  feedback?: {
    title: string;
    description: string;
  };
  content?: ReactNode; // Added to display additional content in the dropdown
  alwaysShowButton?: boolean; // Added to show button even without btnLink
};

export default function ResultCard({
  description,
  passed,
  title,
  btnLink,
  className,
  content,
  alwaysShowButton = false,
  feedback = {
    title: "Your Page could be better",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra accumsan in nisl nisi Arcu cursus vitae congue mauris rhoncus aenean vel elit scelerisque purus viverra accumsan in nisl nisi Arcu cursus vitae purus viverra ",
  },
}: Props) {
  const [showDetails, setShowDetails] = useState(false);
  return (
    <div className="w-full flex flex-col p-4 rounded-lg border border-light-gray overflow-hidden">
      <div className={`flex items-center gap-2.5 ${className}`}>
        <div>
          {passed ? (
            <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
          ) : (
            <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
          )}
        </div>
        <div className="flex-1 flex flex-col gap-2">
          <h5 className="text-secondary font-semibold">{title}</h5>
          <p className="text-sm text-secondary/60">{description}</p>
        </div>
      </div>
      <div className="overflow-hidden">
        {(btnLink || content) && (
          <div
            className={`${
              showDetails ? "max-h-screen mt-4" : "max-h-0"
            } transition-all duration-400 ease-in-out`}
          >
            {content && (
              <div className="mb-4">{content}</div>
            )}
            {btnLink && (
              <FeedbackCard
                title={feedback.title}
                link={btnLink}
                description={feedback.description}
              />
            )}
          </div>
        )}
      </div>
      {(btnLink || alwaysShowButton) && (
        <div className="flex justify-end mt-4">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="btn btn--outline-light"
          >
            Show {showDetails ? "Less" : "More"} Details
          </button>
        </div>
      )}
    </div>
  );
}
